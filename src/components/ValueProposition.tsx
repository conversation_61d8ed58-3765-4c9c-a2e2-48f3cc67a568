export default function ValueProposition() {
  return (
    <section className="py-20 bg-black">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Membership Privileges</h2>
          <p className="text-xl text-gold-400 max-w-3xl mx-auto">
            Experience the pinnacle of luxury travel with benefits that money alone cannot buy
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-gradient-to-br from-gray-900 to-black p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-300 group">
            <div className="w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-center mb-4 text-white">Private Jet Access</h3>
            <p className="text-gray-300 text-center leading-relaxed">Skip commercial flights entirely. Our fleet of private jets ensures complete privacy, luxury, and convenience for every journey.</p>
            <div className="mt-6 text-center">
              <span className="text-gold-400 text-sm font-semibold tracking-wider">UNLIMITED FLIGHTS</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-gray-900 to-black p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-300 group">
            <div className="w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-center mb-4 text-white">Exclusive Properties</h3>
            <p className="text-gray-300 text-center leading-relaxed">Access to private islands, historic castles, and ultra-luxury resorts unavailable to the public. Each property vetted for complete LGBTQ+ affirmation.</p>
            <div className="mt-6 text-center">
              <span className="text-gold-400 text-sm font-semibold tracking-wider">200+ PRIVATE VENUES</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-gray-900 to-black p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-300 group">
            <div className="w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-center mb-4 text-white">Elite Network</h3>
            <p className="text-gray-300 text-center leading-relaxed">Join an exclusive community of influential LGBTQ+ leaders, entrepreneurs, and visionaries. Networking events in the world's most prestigious locations.</p>
            <div className="mt-6 text-center">
              <span className="text-gold-400 text-sm font-semibold tracking-wider">INVITATION ONLY</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  ) ;
}
