export default function Testimonials() {
  const testimonials = [
    {
      id: '1',
      name: '<PERSON>',
      title: 'Tech Entrepreneur',
      location: 'Silicon Valley',
      quote: 'Octavia Opulence has redefined what luxury means to me. The private island experience in the Maldives was beyond anything I could have imagined. Worth every penny of the membership.',
      image: '/images/member-1.jpg',
      memberSince: '2019'
    },
    {
      id: '2',
      name: 'Dr. <PERSON>',
      title: 'Investment Banker',
      location: 'Hong Kong',
      quote: 'The network alone justifies the membership cost. I\'ve made invaluable connections and experienced destinations that simply aren\'t available to the general public.',
      image: '/images/member-2.jpg',
      memberSince: '2020'
    },
    {
      id: '3',
      name: '<PERSON>',
      title: 'Fashion Designer',
      location: 'London',
      quote: 'The level of service and exclusivity is unmatched. From private museum tours to Michelin-starred chefs on demand, Octavia delivers experiences money can\'t typically buy.',
      image: '/images/member-3.jpg',
      memberSince: '2018'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Member Testimonials</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Hear from our exclusive community of discerning travelers
          </p>
          <div className="mt-6">
            <span className="inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full text-gold-600 text-sm font-semibold tracking-wider">
              VERIFIED MEMBERS ONLY
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="bg-gradient-to-br from-gray-50 to-white p-8 rounded-none border-l-4 border-gold-400 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div className="flex flex-col">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold text-xl">{testimonial.name.charAt(0)}</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{testimonial.name}</h3>
                    <p className="text-gold-600 font-semibold text-sm">{testimonial.title}</p>
                    <p className="text-gray-500 text-sm">{testimonial.location}</p>
                  </div>
                </div>
                <blockquote className="text-gray-700 italic text-lg leading-relaxed mb-6">
                  "{testimonial.quote}"
                </blockquote>
                <div className="flex items-center justify-between mt-auto">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-gold-400 rounded-full mr-2"></div>
                    <span className="text-gray-500 text-sm">Member since {testimonial.memberSince}</span>
                  </div>
                  <div className="flex text-gold-400">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
