import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { format } from 'date-fns'
import { getFeaturedPosts } from '@/lib/ghost'

export const BlogPreview = async () => {
  const featuredPosts = await getFeaturedPosts(3);

  return (
    <section className="py-20 bg-gray-50">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#6A0DAD] mb-4">The QueerLuxe Chronicles</h2>
          <p className="text-lg text-gray-700">
            Travel stories, insights, and inspiration from our community
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {featuredPosts.length > 0 ? (
            featuredPosts.map((post) => (
              <div key={post.id} className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all">
                {post.feature_image ? (
                  <div className="h-48 relative">
                    <Image
                      src={post.feature_image}
                      alt={post.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="h-48 bg-[#6A0DAD] relative">
                    <div className="absolute inset-0 flex items-center justify-center text-white">
                      <span>Blog Image</span>
                    </div>
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-[#333333]">{post.title}</h3>
                  <p className="text-gray-600 mb-4">{post.excerpt || 'Read this amazing story...'}</p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    {post.authors && post.authors[0] && (
                      <span>By {post.authors[0].name}</span>
                    )}
                    <time dateTime={post.published_at}>
                      {format(new Date(post.published_at), 'MMM dd, yyyy')}
                    </time>
                  </div>
                  <Link href={`/blog/${post.slug}`} className="text-[#6A0DAD] font-medium hover:underline">
                    Read More →
                  </Link>
                </div>
              </div>
            ))
          ) : (
            // Fallback content when no posts are available
            <>
              <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all">
                <div className="h-48 bg-[#C71585] relative">
                  <div className="absolute inset-0 flex items-center justify-center text-white">
                    <span>Blog Image</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-[#333333]">Finding Queer Joy in Traditional Cultures</h3>
                  <p className="text-gray-600 mb-4">Navigating and celebrating LGBTQ+ experiences in conservative destinations</p>
                  <Link href="/blog" className="text-[#6A0DAD] font-medium hover:underline">Read More →</Link>
                </div>
              </div>

              <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all">
                <div className="h-48 bg-[#008080] relative">
                  <div className="absolute inset-0 flex items-center justify-center text-white">
                    <span>Blog Image</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-[#333333]">The Ultimate Pride Calendar 2025</h3>
                  <p className="text-gray-600 mb-4">Your guide to the most fabulous Pride celebrations around the world</p>
                  <Link href="/blog" className="text-[#6A0DAD] font-medium hover:underline">Read More →</Link>
                </div>
              </div>

              <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all">
                <div className="h-48 bg-[#6A0DAD] relative">
                  <div className="absolute inset-0 flex items-center justify-center text-white">
                    <span>Blog Image</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-[#333333]">Luxury Travel for Every Body</h3>
                  <p className="text-gray-600 mb-4">How QueerLuxe is redefining inclusive luxury experiences</p>
                  <Link href="/blog" className="text-[#6A0DAD] font-medium hover:underline">Read More →</Link>
                </div>
              </div>
            </>
          )}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/blog"
            className="inline-block bg-transparent border-2 border-[#6A0DAD] text-[#6A0DAD] px-8 py-3 rounded-full font-medium hover:bg-[#6A0DAD] hover:text-white transition-all"
          >
            View All Articles
          </Link>
        </div>
      </div>
    </section>
  )
}
