"use client";

'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Hero() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      title: "OCTAVIA OPULENCE",
      subtitle: "Where Luxury Meets Authenticity",
      description: "Curated experiences for the discerning LGBTQ+ traveler who demands nothing less than extraordinary.",
      cta: "Begin Your Journey",
      accent: "Travel Authentically. Live Luxuriously."
    },
    {
      title: "BESPOKE ADVENTURES",
      subtitle: "Tailored to Your Vision",
      description: "Every journey is meticulously crafted to reflect your unique story and desires.",
      cta: "Discover More",
      accent: "Your Story. Our Expertise."
    },
    {
      title: "GLOBAL SANCTUARY",
      subtitle: "Safe Havens Worldwide",
      description: "Explore the world with confidence through our network of LGBTQ+ affirming destinations.",
      cta: "Explore Destinations",
      accent: "Belong Anywhere."
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 6000);
    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-black">
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-black to-pink-900 opacity-90"></div>

      {/* Geometric Pattern Overlay */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-32 h-32 border border-gold-400 rotate-45 animate-pulse"></div>
        <div className="absolute bottom-32 right-32 w-24 h-24 border border-purple-400 rotate-12 animate-bounce"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full opacity-20 animate-ping"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
        {/* Accent Text */}
        <div className="mb-6 overflow-hidden">
          <p className="text-gold-400 text-sm md:text-base font-light tracking-[0.3em] uppercase animate-fade-in-up opacity-90">
            {slides[currentSlide].accent}
          </p>
        </div>

        {/* Main Title */}
        <div className="mb-8 overflow-hidden">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight animate-fade-in-up animation-delay-200">
            <span className="bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
              {slides[currentSlide].title}
            </span>
          </h1>
        </div>

        {/* Subtitle */}
        <div className="mb-6 overflow-hidden">
          <h2 className="text-xl md:text-3xl lg:text-4xl font-light text-purple-200 animate-fade-in-up animation-delay-400">
            {slides[currentSlide].subtitle}
          </h2>
        </div>

        {/* Description */}
        <div className="mb-12 overflow-hidden">
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-600">
            {slides[currentSlide].description}
          </p>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up animation-delay-800">
          <Link
            href="/destinations"
            className="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-none border-2 border-transparent hover:border-gold-400 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
          >
            <span className="relative z-10">{slides[currentSlide].cta}</span>
            <div className="absolute inset-0 bg-gradient-to-r from-gold-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </Link>

          <Link
            href="/consultation"
            className="group px-8 py-4 border-2 border-gold-400 text-gold-400 font-semibold rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300 transform hover:scale-105"
          >
            Book Consultation
          </Link>
        </div>

        {/* Slide Indicators */}
        <div className="flex justify-center mt-16 space-x-3">
          {slides.map((_, index) => (
            <button
              key={index}
              type="button"
              onClick={() => setCurrentSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? 'bg-gold-400 scale-125'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
