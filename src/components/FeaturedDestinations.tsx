import Link from 'next/link';

export default function FeaturedDestinations() {
  const destinations = [
    {
      id: '1',
      name: 'Private Island, Maldives',
      image: '/images/maldives-private.jpg',
      description: 'Your own 50-acre private island with overwater villas, personal chef, and complete privacy. Helicopter transfers included.',
      exclusivity: 'Members Only',
      price: 'Included in Membership'
    },
    {
      id: '2',
      name: 'Château de Versailles, France',
      image: '/images/versailles-private.jpg',
      description: 'After-hours private tours of Versailles with Michelin-starred dining in the Hall of Mirrors. Exclusive access to restricted areas.',
      exclusivity: 'Ultra-Private',
      price: 'Invitation Only'
    },
    {
      id: '3',
      name: 'Antarctic Luxury Expedition',
      image: '/images/antarctica-luxury.jpg',
      description: 'Private yacht expedition to Antarctica with world-class naturalists, gourmet cuisine, and heated observation decks.',
      exclusivity: 'Limited to 12 Guests',
      price: 'Complimentary'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Exclusive Experiences</h2>
          <p className="text-xl text-gold-400 max-w-4xl mx-auto leading-relaxed">
            Access to the world's most coveted destinations and experiences, available only to Octavia Opulence members
          </p>
          <div className="mt-6">
            <span className="inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full text-gold-400 text-sm font-semibold tracking-wider">
              VALUED AT OVER $2M ANNUALLY
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {destinations.map((destination) => (
            <div key={destination.id} className="bg-gradient-to-br from-gray-800 to-black rounded-none border border-gold-400/20 overflow-hidden hover:border-gold-400/50 transition-all duration-500 group">
              <div className="h-64 bg-gradient-to-br from-gold-400/20 to-purple-900/30 relative overflow-hidden">
                {/* Luxury placeholder */}
                <div className="absolute inset-0 bg-black/40"></div>
                <div className="absolute top-4 left-4 bg-gold-400 text-black px-3 py-1 rounded-none text-xs font-bold tracking-wider">
                  {destination.exclusivity}
                </div>
                <div className="absolute bottom-4 right-4 bg-black/70 text-gold-400 px-3 py-1 rounded-none text-xs font-semibold">
                  {destination.price}
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-white/80 text-center">
                    <div className="w-16 h-16 border-2 border-gold-400/50 rounded-full flex items-center justify-center mx-auto mb-2">
                      <span className="text-gold-400 text-2xl">✈</span>
                    </div>
                    <span className="text-sm font-light">Ultra-Luxury Experience</span>
                  </div>
                </div>
              </div>
              <div className="p-8">
                <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-gold-400 transition-colors">{destination.name}</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">{destination.description}</p>
                <Link href={`/experiences/${destination.id}`} className="inline-flex items-center text-gold-400 font-semibold hover:text-white transition-colors group">
                  Reserve Experience
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-16">
          <Link href="/exclusive-portfolio" className="inline-flex items-center px-10 py-4 border-2 border-gold-400 text-gold-400 font-semibold rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300 group">
            View Complete Portfolio
            <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
