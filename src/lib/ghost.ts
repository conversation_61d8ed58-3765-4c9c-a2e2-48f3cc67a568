import GhostContentAPI from '@tryghost/content-api';

// Create API instance with site credentials
const api = new GhostContentAPI({
  url: 'https://rainbow-millipede.pikapod.net',
  key: 'feb948c4d47fa122219ac0e131',
  version: 'v5.0'
});

// Helper function to get all posts
export async function getPosts() {
  try {
    const posts = await api.posts.browse({
      limit: 'all',
      include: 'tags,authors',
      order: 'published_at DESC'
    });
    return posts;
  } catch (error) {
    console.error('Error fetching posts:', error);
    return [];
  }
}

// Helper function to get a single post by slug
export async function getPostBySlug(slug) {
  try {
    const post = await api.posts.read({
      slug: slug
    }, {
      include: 'tags,authors'
    });
    return post;
  } catch (error) {
    console.error('Error fetching post:', error);
    return null;
  }
}

// Helper function to get featured posts
export async function getFeaturedPosts(limit = 3) {
  try {
    const posts = await api.posts.browse({
      limit: limit,
      filter: 'featured:true',
      include: 'tags,authors',
      order: 'published_at DESC'
    });
    return posts;
  } catch (error) {
    console.error('Error fetching featured posts:', error);
    return [];
  }
}

// Helper function to get recent posts
export async function getRecentPosts(limit = 6) {
  try {
    const posts = await api.posts.browse({
      limit: limit,
      include: 'tags,authors',
      order: 'published_at DESC'
    });
    return posts;
  } catch (error) {
    console.error('Error fetching recent posts:', error);
    return [];
  }
}

// Helper function to get posts by tag
export async function getPostsByTag(tagSlug, limit = 10) {
  try {
    const posts = await api.posts.browse({
      limit: limit,
      filter: `tag:${tagSlug}`,
      include: 'tags,authors',
      order: 'published_at DESC'
    });
    return posts;
  } catch (error) {
    console.error('Error fetching posts by tag:', error);
    return [];
  }
}

export default api;
