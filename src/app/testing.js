{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true
  }
}

module.exports = nextConfiimport Hero from '@/components/Hero'
import ValueProposition from '@/components/ValueProposition'
import FeaturedDestinations from '@/components/FeaturedDestinations'
import Testimonials from '@/components/Testimonials'
import HowItWorks from '@/components/HowItWorks'g