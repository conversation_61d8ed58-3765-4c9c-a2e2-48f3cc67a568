import { getPosts } from '@/lib/ghost';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';

export const metadata = {
  title: 'Blog - QueerLuxe Travel Studio',
  description: 'Travel stories, insights, and inspiration from our LGBTQ+ community',
};

export default async function BlogPage() {
  const posts = await getPosts();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">The QueerLuxe Chronicles</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Travel stories, insights, and inspiration from our community
          </p>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {posts.length === 0 ? (
            <div className="text-center py-16">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">No posts found</h2>
              <p className="text-gray-600">Check back soon for exciting travel stories and insights!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                  {/* Featured Image */}
                  {post.feature_image && (
                    <div className="relative h-48 w-full">
                      <Image
                        src={post.feature_image}
                        alt={post.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  
                  {/* Content */}
                  <div className="p-6">
                    {/* Tags */}
                    {post.tags && post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-3">
                        {post.tags.slice(0, 2).map((tag) => (
                          <span
                            key={tag.id}
                            className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full"
                          >
                            {tag.name}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Title */}
                    <h2 className="text-xl font-bold text-gray-800 mb-3 line-clamp-2">
                      <Link 
                        href={`/blog/${post.slug}`}
                        className="hover:text-purple-600 transition-colors"
                      >
                        {post.title}
                      </Link>
                    </h2>

                    {/* Excerpt */}
                    {post.excerpt && (
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                    )}

                    {/* Meta Information */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        {post.authors && post.authors[0] && (
                          <span>By {post.authors[0].name}</span>
                        )}
                      </div>
                      <time dateTime={post.published_at}>
                        {format(new Date(post.published_at), 'MMM dd, yyyy')}
                      </time>
                    </div>

                    {/* Read More Link */}
                    <div className="mt-4">
                      <Link
                        href={`/blog/${post.slug}`}
                        className="text-purple-600 font-medium hover:text-purple-800 transition-colors"
                      >
                        Read More →
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
