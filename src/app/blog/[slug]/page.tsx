import { getPostBySlug, getRecentPosts } from '@/lib/ghost';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { notFound } from 'next/navigation';

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps) {
  const { slug } = params;
  const post = await getPostBySlug(slug);
  
  if (!post) {
    return {
      title: 'Post Not Found - QueerLuxe Travel Studio',
    };
  }

  return {
    title: `${post.title} - QueerLuxe Travel Studio`,
    description: post.excerpt || post.meta_description,
    openGraph: {
      title: post.title,
      description: post.excerpt || post.meta_description,
      images: post.feature_image ? [post.feature_image] : [],
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = params;
  const [post, recentPosts] = await Promise.all([
    getPostBySlug(slug),
    getRecentPosts(3)
  ]);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Featured Image */}
      <section className="relative">
        {post.feature_image && (
          <div className="relative h-64 md:h-96 w-full overflow-hidden">
            <Image
              src={post.feature_image}
              alt={post.title}
              width={1200}
              height={400}
              className="object-cover w-full h-full"
              priority
              sizes="100vw"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </div>
        )}
        
        {/* Article Header */}
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="mb-6">
              <Link href="/blog" className="text-purple-600 hover:text-purple-800">
                ← Back to Blog
              </Link>
            </nav>

            {/* Tags */}
            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.map((tag) => (
                  <span
                    key={tag.id}
                    className="bg-purple-100 text-purple-800 text-sm px-3 py-1 rounded-full"
                  >
                    {tag.name}
                  </span>
                ))}
              </div>
            )}

            {/* Title */}
            <h1 className="text-3xl md:text-5xl font-bold text-gray-800 mb-6">
              {post.title}
            </h1>

            {/* Meta Information */}
            <div className="flex items-center text-gray-600 mb-8">
              {post.authors && post.authors[0] && (
                <div className="flex items-center mr-6">
                  {post.authors[0].profile_image && (
                    <div className="relative w-10 h-10 mr-3">
                      <Image
                        src={post.authors[0].profile_image}
                        alt={post.authors[0].name}
                        width={40}
                        height={40}
                        className="rounded-full object-cover w-full h-full"
                      />
                    </div>
                  )}
                  <span>By {post.authors[0].name}</span>
                </div>
              )}
              <time dateTime={post.published_at}>
                {format(new Date(post.published_at), 'MMMM dd, yyyy')}
              </time>
              <span className="mx-2">•</span>
              <span>{post.reading_time} min read</span>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <article className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div 
              className="prose prose-lg max-w-none prose-purple prose-headings:text-gray-800 prose-p:text-gray-700 prose-a:text-purple-600 prose-a:no-underline hover:prose-a:underline"
              dangerouslySetInnerHTML={{ __html: post.html }}
            />
          </div>
        </div>
      </article>

      {/* Related Posts */}
      {recentPosts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">
                More Stories
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {recentPosts.filter(p => p.slug !== post.slug).slice(0, 3).map((relatedPost) => (
                  <article key={relatedPost.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    {relatedPost.feature_image && (
                      <div className="relative h-48 w-full overflow-hidden">
                        <Image
                          src={relatedPost.feature_image}
                          alt={relatedPost.title}
                          width={400}
                          height={192}
                          className="object-cover w-full h-full"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                    )}
                    
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-3">
                        <Link 
                          href={`/blog/${relatedPost.slug}`}
                          className="hover:text-purple-600 transition-colors"
                        >
                          {relatedPost.title}
                        </Link>
                      </h3>
                      
                      {relatedPost.excerpt && (
                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {relatedPost.excerpt}
                        </p>
                      )}
                      
                      <Link
                        href={`/blog/${relatedPost.slug}`}
                        className="text-purple-600 font-medium hover:text-purple-800 transition-colors"
                      >
                        Read More →
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
