@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #6A0DAD; /* <PERSON><PERSON> Purple */
  --color-secondary: #D4AF37; /* Gold Accent */
  --color-tertiary: #008080; /* Deep Teal */
  --color-accent: #C71585; /* Rich Magenta */
  --color-background: #FFFFFF;
  --color-text: #333333;
}

body {
  color: var(--color-text);
  background: var(--color-background);
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
  
  body {
    font-family: 'Montserrat', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-[#6A0DAD] text-white px-6 py-3 rounded-md hover:bg-opacity-90 transition-all;
  }
  
  .btn-secondary {
    @apply bg-[#D4AF37] text-white px-6 py-3 rounded-md hover:bg-opacity-90 transition-all;
  }
  
  .btn-outline {
    @apply border-2 border-[#6A0DAD] text-[#6A0DAD] px-6 py-3 rounded-md hover:bg-[#6A0DAD] hover:text-white transition-all;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Blog content styling */
  .prose {
    @apply text-gray-700 leading-relaxed;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    @apply text-gray-800 font-bold mt-8 mb-4;
  }

  .prose h1 {
    @apply text-3xl;
  }

  .prose h2 {
    @apply text-2xl;
  }

  .prose h3 {
    @apply text-xl;
  }

  .prose p {
    @apply mb-6 text-lg;
  }

  .prose a {
    @apply text-purple-600 hover:text-purple-800 underline;
  }

  .prose img {
    @apply rounded-lg shadow-lg my-8 max-w-full h-auto;
  }

  .prose blockquote {
    @apply border-l-4 border-purple-600 pl-6 italic text-gray-600 my-6;
  }

  .prose ul, .prose ol {
    @apply mb-6 pl-6;
  }

  .prose li {
    @apply mb-2;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Global image constraints */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Ensure Next.js Image components are properly constrained */
  .next-image {
    max-width: 100%;
    height: auto;
  }

  /* Hero Animations */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
  }

  .animation-delay-200 {
    animation-delay: 0.2s;
    opacity: 0;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
    opacity: 0;
  }

  .animation-delay-600 {
    animation-delay: 0.6s;
    opacity: 0;
  }

  .animation-delay-800 {
    animation-delay: 0.8s;
    opacity: 0;
  }
}
