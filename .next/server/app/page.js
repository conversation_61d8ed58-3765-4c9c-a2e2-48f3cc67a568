/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flevihankins%2Ftheplatform-1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flevihankins%2Ftheplatform-1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/theplatform-1/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/theplatform-1/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/theplatform-1/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flevihankins%2Ftheplatform-1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(rsc)/./src/components/Hero.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZhcHAtZGlyJTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmxldmloYW5raW5zJTJGdGhlcGxhdGZvcm0tMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGaW1hZ2UtY29tcG9uZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGc3JjJTJGY29tcG9uZW50cyUyRkhlcm8udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQW1IO0FBQ25IO0FBQ0Esc05BQXNIO0FBQ3RIO0FBQ0EsOEpBQTBIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbGV2aWhhbmtpbnMvdGhlcGxhdGZvcm0tMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hcHAtZGlyL2xpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9sZXZpaGFua2lucy90aGVwbGF0Zm9ybS0xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2ltYWdlLWNvbXBvbmVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9sZXZpaGFua2lucy90aGVwbGF0Zm9ybS0xL3NyYy9jb21wb25lbnRzL0hlcm8udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(rsc)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsZXZpaGFua2lucyUyRnRoZXBsYXRmb3JtLTElMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvbGV2aWhhbmtpbnMvdGhlcGxhdGZvcm0tMS9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"66a220db2036\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbGV2aWhhbmtpbnMvdGhlcGxhdGZvcm0tMS9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjZhMjIwZGIyMDM2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'QueerLuxe Travel Studio',\n    description: 'Bespoke, luxurious travel by and for the LGBTQ+ community and allies'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/app/layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUMwQjtBQUNKO0FBSXJDLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlOzs4QkFDOUIsOERBQUNDLDhEQUFVQTs7Ozs7Z0JBQ1ZNOzhCQUNELDhEQUFDTCxzREFBTUE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJZiIsInNvdXJjZXMiOlsiL1VzZXJzL2xldmloYW5raW5zL3RoZXBsYXRmb3JtLTEvc3JjL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IE5hdmlnYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL05hdmlnYXRpb24nXG5pbXBvcnQgeyBGb290ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvRm9vdGVyJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnUXVlZXJMdXhlIFRyYXZlbCBTdHVkaW8nLFxuICBkZXNjcmlwdGlvbjogJ0Jlc3Bva2UsIGx1eHVyaW91cyB0cmF2ZWwgYnkgYW5kIGZvciB0aGUgTEdCVFErIGNvbW11bml0eSBhbmQgYWxsaWVzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxOYXZpZ2F0aW9uIC8+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPEZvb3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiTmF2aWdhdGlvbiIsIkZvb3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_ValueProposition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ValueProposition */ \"(rsc)/./src/components/ValueProposition.tsx\");\n/* harmony import */ var _components_FeaturedDestinations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FeaturedDestinations */ \"(rsc)/./src/components/FeaturedDestinations.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Testimonials */ \"(rsc)/./src/components/Testimonials.tsx\");\n/* harmony import */ var _components_HowItWorks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/HowItWorks */ \"(rsc)/./src/components/HowItWorks.tsx\");\n/* harmony import */ var _components_BlogPreview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/BlogPreview */ \"(rsc)/./src/components/BlogPreview.tsx\");\n/* harmony import */ var _components_Newsletter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Newsletter */ \"(rsc)/./src/components/Newsletter.tsx\");\n\n\n\n\n\n\n\n\nasync function Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ValueProposition__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeaturedDestinations__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HowItWorks__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlogPreview__WEBPACK_IMPORTED_MODULE_6__.BlogPreview, {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Newsletter__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/theplatform-1/src/app/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQW9DO0FBQ3dCO0FBQ1E7QUFDaEI7QUFDSjtBQUNNO0FBQ047QUFFakMsZUFBZU87SUFDNUIscUJBQ0UsOERBQUNDO1FBQUtDLFdBQVU7OzBCQUNkLDhEQUFDVCx3REFBSUE7Ozs7OzBCQUNMLDhEQUFDQyxvRUFBZ0JBOzs7OzswQkFDakIsOERBQUNDLHdFQUFvQkE7Ozs7OzBCQUNyQiw4REFBQ0MsZ0VBQVlBOzs7OzswQkFDYiw4REFBQ0MsOERBQVVBOzs7OzswQkFDWCw4REFBQ0MsZ0VBQVdBOzs7OzswQkFDWiw4REFBQ0MsOERBQVVBOzs7Ozs7Ozs7OztBQUdqQiIsInNvdXJjZXMiOlsiL1VzZXJzL2xldmloYW5raW5zL3RoZXBsYXRmb3JtLTEvc3JjL2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSGVybyBmcm9tICdAL2NvbXBvbmVudHMvSGVybydcbmltcG9ydCBWYWx1ZVByb3Bvc2l0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9WYWx1ZVByb3Bvc2l0aW9uJ1xuaW1wb3J0IEZlYXR1cmVkRGVzdGluYXRpb25zIGZyb20gJ0AvY29tcG9uZW50cy9GZWF0dXJlZERlc3RpbmF0aW9ucydcbmltcG9ydCBUZXN0aW1vbmlhbHMgZnJvbSAnQC9jb21wb25lbnRzL1Rlc3RpbW9uaWFscydcbmltcG9ydCBIb3dJdFdvcmtzIGZyb20gJ0AvY29tcG9uZW50cy9Ib3dJdFdvcmtzJ1xuaW1wb3J0IHsgQmxvZ1ByZXZpZXcgfSBmcm9tICdAL2NvbXBvbmVudHMvQmxvZ1ByZXZpZXcnXG5pbXBvcnQgTmV3c2xldHRlciBmcm9tICdAL2NvbXBvbmVudHMvTmV3c2xldHRlcidcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW5cIj5cbiAgICAgIDxIZXJvIC8+XG4gICAgICA8VmFsdWVQcm9wb3NpdGlvbiAvPlxuICAgICAgPEZlYXR1cmVkRGVzdGluYXRpb25zIC8+XG4gICAgICA8VGVzdGltb25pYWxzIC8+XG4gICAgICA8SG93SXRXb3JrcyAvPlxuICAgICAgPEJsb2dQcmV2aWV3IC8+XG4gICAgICA8TmV3c2xldHRlciAvPlxuICAgIDwvbWFpbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkhlcm8iLCJWYWx1ZVByb3Bvc2l0aW9uIiwiRmVhdHVyZWREZXN0aW5hdGlvbnMiLCJUZXN0aW1vbmlhbHMiLCJIb3dJdFdvcmtzIiwiQmxvZ1ByZXZpZXciLCJOZXdzbGV0dGVyIiwiSG9tZSIsIm1haW4iLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/BlogPreview.tsx":
/*!****************************************!*\
  !*** ./src/components/BlogPreview.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogPreview: () => (/* binding */ BlogPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_ghost__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ghost */ \"(rsc)/./src/lib/ghost.ts\");\n\n\n\n\n\n\nconst BlogPreview = async ()=>{\n    const featuredPosts = await (0,_lib_ghost__WEBPACK_IMPORTED_MODULE_4__.getFeaturedPosts)(3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-3xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-[#6A0DAD] mb-4\",\n                            children: \"The QueerLuxe Chronicles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700\",\n                            children: \"Travel stories, insights, and inspiration from our community\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: featuredPosts.length > 0 ? featuredPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all\",\n                            children: [\n                                post.feature_image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 relative overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: post.feature_image,\n                                        alt: post.title,\n                                        width: 400,\n                                        height: 192,\n                                        className: \"object-cover w-full h-full\",\n                                        sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-[#6A0DAD] relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Blog Image\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-2 text-[#333333]\",\n                                            children: post.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: post.excerpt || 'Read this amazing story...'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                                            children: [\n                                                post.authors && post.authors[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"By \",\n                                                        post.authors[0].name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                    dateTime: post.published_at,\n                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(new Date(post.published_at), 'MMM dd, yyyy')\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `/blog/${post.slug}`,\n                                            className: \"text-[#6A0DAD] font-medium hover:underline\",\n                                            children: \"Read More →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, post.id, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 15\n                        }, undefined)) : // Fallback content when no posts are available\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48 bg-[#C71585] relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Blog Image\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2 text-[#333333]\",\n                                                children: \"Finding Queer Joy in Traditional Cultures\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Navigating and celebrating LGBTQ+ experiences in conservative destinations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-[#6A0DAD] font-medium hover:underline\",\n                                                children: \"Read More →\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48 bg-[#008080] relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Blog Image\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2 text-[#333333]\",\n                                                children: \"The Ultimate Pride Calendar 2025\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Your guide to the most fabulous Pride celebrations around the world\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-[#6A0DAD] font-medium hover:underline\",\n                                                children: \"Read More →\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48 bg-[#6A0DAD] relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Blog Image\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2 text-[#333333]\",\n                                                children: \"Luxury Travel for Every Body\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"How QueerLuxe is redefining inclusive luxury experiences\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-[#6A0DAD] font-medium hover:underline\",\n                                                children: \"Read More →\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/blog\",\n                        className: \"inline-block bg-transparent border-2 border-[#6A0DAD] text-[#6A0DAD] px-8 py-3 rounded-full font-medium hover:bg-[#6A0DAD] hover:text-white transition-all\",\n                        children: \"View All Articles\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/BlogPreview.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9CbG9nUHJldmlldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBeUI7QUFDRztBQUNFO0FBQ0c7QUFDYTtBQUV2QyxNQUFNSyxjQUFjO0lBQ3pCLE1BQU1DLGdCQUFnQixNQUFNRiw0REFBZ0JBLENBQUM7SUFFN0MscUJBQ0UsOERBQUNHO1FBQVFDLFdBQVU7a0JBQ2pCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDRTs0QkFBR0YsV0FBVTtzQ0FBcUQ7Ozs7OztzQ0FDbkUsOERBQUNHOzRCQUFFSCxXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7OzhCQUt2Qyw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ1pGLGNBQWNNLE1BQU0sR0FBRyxJQUN0Qk4sY0FBY08sR0FBRyxDQUFDLENBQUNDLHFCQUNqQiw4REFBQ0w7NEJBQWtCRCxXQUFVOztnQ0FDMUJNLEtBQUtDLGFBQWEsaUJBQ2pCLDhEQUFDTjtvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ04sa0RBQUtBO3dDQUNKYyxLQUFLRixLQUFLQyxhQUFhO3dDQUN2QkUsS0FBS0gsS0FBS0ksS0FBSzt3Q0FDZkMsT0FBTzt3Q0FDUEMsUUFBUTt3Q0FDUlosV0FBVTt3Q0FDVmEsT0FBTTs7Ozs7Ozs7Ozs4REFJViw4REFBQ1o7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNDO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDYztzREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ2I7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDZTs0Q0FBR2YsV0FBVTtzREFBeUNNLEtBQUtJLEtBQUs7Ozs7OztzREFDakUsOERBQUNQOzRDQUFFSCxXQUFVO3NEQUFzQk0sS0FBS1UsT0FBTyxJQUFJOzs7Ozs7c0RBQ25ELDhEQUFDZjs0Q0FBSUQsV0FBVTs7Z0RBQ1pNLEtBQUtXLE9BQU8sSUFBSVgsS0FBS1csT0FBTyxDQUFDLEVBQUUsa0JBQzlCLDhEQUFDSDs7d0RBQUs7d0RBQUlSLEtBQUtXLE9BQU8sQ0FBQyxFQUFFLENBQUNDLElBQUk7Ozs7Ozs7OERBRWhDLDhEQUFDQztvREFBS0MsVUFBVWQsS0FBS2UsWUFBWTs4REFDOUIxQiw4RUFBTUEsQ0FBQyxJQUFJMkIsS0FBS2hCLEtBQUtlLFlBQVksR0FBRzs7Ozs7Ozs7Ozs7O3NEQUd6Qyw4REFBQzVCLGtEQUFJQTs0Q0FBQzhCLE1BQU0sQ0FBQyxNQUFNLEVBQUVqQixLQUFLa0IsSUFBSSxFQUFFOzRDQUFFeEIsV0FBVTtzREFBNkM7Ozs7Ozs7Ozs7Ozs7MkJBOUJuRk0sS0FBS21CLEVBQUU7Ozs7eUNBcUNuQiwrQ0FBK0M7a0NBQy9DOzswQ0FDRSw4REFBQ3hCO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDYzswREFBSzs7Ozs7Ozs7Ozs7Ozs7OztrREFHViw4REFBQ2I7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDZTtnREFBR2YsV0FBVTswREFBd0M7Ozs7OzswREFDdEQsOERBQUNHO2dEQUFFSCxXQUFVOzBEQUFxQjs7Ozs7OzBEQUNsQyw4REFBQ1Asa0RBQUlBO2dEQUFDOEIsTUFBSztnREFBUXZCLFdBQVU7MERBQTZDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSTlFLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ2M7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR1YsOERBQUNiO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ2U7Z0RBQUdmLFdBQVU7MERBQXdDOzs7Ozs7MERBQ3RELDhEQUFDRztnREFBRUgsV0FBVTswREFBcUI7Ozs7OzswREFDbEMsOERBQUNQLGtEQUFJQTtnREFBQzhCLE1BQUs7Z0RBQVF2QixXQUFVOzBEQUE2Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUk5RSw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUNjOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUdWLDhEQUFDYjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNlO2dEQUFHZixXQUFVOzBEQUF3Qzs7Ozs7OzBEQUN0RCw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQXFCOzs7Ozs7MERBQ2xDLDhEQUFDUCxrREFBSUE7Z0RBQUM4QixNQUFLO2dEQUFRdkIsV0FBVTswREFBNkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3BGLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ1Asa0RBQUlBO3dCQUNIOEIsTUFBSzt3QkFDTHZCLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWCxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvbGV2aWhhbmtpbnMvdGhlcGxhdGZvcm0tMS9zcmMvY29tcG9uZW50cy9CbG9nUHJldmlldy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnXG5pbXBvcnQgeyBmb3JtYXQgfSBmcm9tICdkYXRlLWZucydcbmltcG9ydCB7IGdldEZlYXR1cmVkUG9zdHMgfSBmcm9tICdAL2xpYi9naG9zdCdcblxuZXhwb3J0IGNvbnN0IEJsb2dQcmV2aWV3ID0gYXN5bmMgKCkgPT4ge1xuICBjb25zdCBmZWF0dXJlZFBvc3RzID0gYXdhaXQgZ2V0RmVhdHVyZWRQb3N0cygzKTtcblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLWN1c3RvbVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LTN4bCBteC1hdXRvIG1iLTE2XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LVsjNkEwREFEXSBtYi00XCI+VGhlIFF1ZWVyTHV4ZSBDaHJvbmljbGVzPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgIFRyYXZlbCBzdG9yaWVzLCBpbnNpZ2h0cywgYW5kIGluc3BpcmF0aW9uIGZyb20gb3VyIGNvbW11bml0eVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAge2ZlYXR1cmVkUG9zdHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgIGZlYXR1cmVkUG9zdHMubWFwKChwb3N0KSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtwb3N0LmlkfSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tYWxsXCI+XG4gICAgICAgICAgICAgICAge3Bvc3QuZmVhdHVyZV9pbWFnZSA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00OCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtwb3N0LmZlYXR1cmVfaW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgYWx0PXtwb3N0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPXs0MDB9XG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXsxOTJ9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyIHctZnVsbCBoLWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemVzPVwiKG1heC13aWR0aDogNzY4cHgpIDEwMHZ3LCAobWF4LXdpZHRoOiAxMjAwcHgpIDUwdncsIDMzdndcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00OCBiZy1bIzZBMERBRF0gcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5CbG9nIEltYWdlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi0yIHRleHQtWyMzMzMzMzNdXCI+e3Bvc3QudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPntwb3N0LmV4Y2VycHQgfHwgJ1JlYWQgdGhpcyBhbWF6aW5nIHN0b3J5Li4uJ308L3A+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICB7cG9zdC5hdXRob3JzICYmIHBvc3QuYXV0aG9yc1swXSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Qnkge3Bvc3QuYXV0aG9yc1swXS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPHRpbWUgZGF0ZVRpbWU9e3Bvc3QucHVibGlzaGVkX2F0fT5cbiAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0KG5ldyBEYXRlKHBvc3QucHVibGlzaGVkX2F0KSwgJ01NTSBkZCwgeXl5eScpfVxuICAgICAgICAgICAgICAgICAgICA8L3RpbWU+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2AvYmxvZy8ke3Bvc3Quc2x1Z31gfSBjbGFzc05hbWU9XCJ0ZXh0LVsjNkEwREFEXSBmb250LW1lZGl1bSBob3Zlcjp1bmRlcmxpbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgUmVhZCBNb3JlIOKGklxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpXG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIC8vIEZhbGxiYWNrIGNvbnRlbnQgd2hlbiBubyBwb3N0cyBhcmUgYXZhaWxhYmxlXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGxcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNDggYmctWyNDNzE1ODVdIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5CbG9nIEltYWdlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi0yIHRleHQtWyMzMzMzMzNdXCI+RmluZGluZyBRdWVlciBKb3kgaW4gVHJhZGl0aW9uYWwgQ3VsdHVyZXM8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+TmF2aWdhdGluZyBhbmQgY2VsZWJyYXRpbmcgTEdCVFErIGV4cGVyaWVuY2VzIGluIGNvbnNlcnZhdGl2ZSBkZXN0aW5hdGlvbnM8L3A+XG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Jsb2dcIiBjbGFzc05hbWU9XCJ0ZXh0LVsjNkEwREFEXSBmb250LW1lZGl1bSBob3Zlcjp1bmRlcmxpbmVcIj5SZWFkIE1vcmUg4oaSPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGxcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNDggYmctWyMwMDgwODBdIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5CbG9nIEltYWdlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi0yIHRleHQtWyMzMzMzMzNdXCI+VGhlIFVsdGltYXRlIFByaWRlIENhbGVuZGFyIDIwMjU8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+WW91ciBndWlkZSB0byB0aGUgbW9zdCBmYWJ1bG91cyBQcmlkZSBjZWxlYnJhdGlvbnMgYXJvdW5kIHRoZSB3b3JsZDwvcD5cbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYmxvZ1wiIGNsYXNzTmFtZT1cInRleHQtWyM2QTBEQURdIGZvbnQtbWVkaXVtIGhvdmVyOnVuZGVybGluZVwiPlJlYWQgTW9yZSDihpI8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00OCBiZy1bIzZBMERBRF0gcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPkJsb2cgSW1hZ2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTIgdGV4dC1bIzMzMzMzM11cIj5MdXh1cnkgVHJhdmVsIGZvciBFdmVyeSBCb2R5PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPkhvdyBRdWVlckx1eGUgaXMgcmVkZWZpbmluZyBpbmNsdXNpdmUgbHV4dXJ5IGV4cGVyaWVuY2VzPC9wPlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9ibG9nXCIgY2xhc3NOYW1lPVwidGV4dC1bIzZBMERBRF0gZm9udC1tZWRpdW0gaG92ZXI6dW5kZXJsaW5lXCI+UmVhZCBNb3JlIOKGkjwvTGluaz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG10LTEyXCI+XG4gICAgICAgICAgPExpbmtcbiAgICAgICAgICAgIGhyZWY9XCIvYmxvZ1wiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYmctdHJhbnNwYXJlbnQgYm9yZGVyLTIgYm9yZGVyLVsjNkEwREFEXSB0ZXh0LVsjNkEwREFEXSBweC04IHB5LTMgcm91bmRlZC1mdWxsIGZvbnQtbWVkaXVtIGhvdmVyOmJnLVsjNkEwREFEXSBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBWaWV3IEFsbCBBcnRpY2xlc1xuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L3NlY3Rpb24+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxpbmsiLCJJbWFnZSIsImZvcm1hdCIsImdldEZlYXR1cmVkUG9zdHMiLCJCbG9nUHJldmlldyIsImZlYXR1cmVkUG9zdHMiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDIiLCJwIiwibGVuZ3RoIiwibWFwIiwicG9zdCIsImZlYXR1cmVfaW1hZ2UiLCJzcmMiLCJhbHQiLCJ0aXRsZSIsIndpZHRoIiwiaGVpZ2h0Iiwic2l6ZXMiLCJzcGFuIiwiaDMiLCJleGNlcnB0IiwiYXV0aG9ycyIsIm5hbWUiLCJ0aW1lIiwiZGF0ZVRpbWUiLCJwdWJsaXNoZWRfYXQiLCJEYXRlIiwiaHJlZiIsInNsdWciLCJpZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/BlogPreview.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/FeaturedDestinations.tsx":
/*!*************************************************!*\
  !*** ./src/components/FeaturedDestinations.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturedDestinations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction FeaturedDestinations() {\n    const destinations = [\n        {\n            id: '1',\n            name: 'Private Island, Maldives',\n            image: '/images/maldives-private.jpg',\n            description: 'Your own 50-acre private island with overwater villas, personal chef, and complete privacy. Helicopter transfers included.',\n            exclusivity: 'Members Only',\n            price: 'Included in Membership'\n        },\n        {\n            id: '2',\n            name: 'Château de Versailles, France',\n            image: '/images/versailles-private.jpg',\n            description: 'After-hours private tours of Versailles with Michelin-starred dining in the Hall of Mirrors. Exclusive access to restricted areas.',\n            exclusivity: 'Ultra-Private',\n            price: 'Invitation Only'\n        },\n        {\n            id: '3',\n            name: 'Antarctic Luxury Expedition',\n            image: '/images/antarctica-luxury.jpg',\n            description: 'Private yacht expedition to Antarctica with world-class naturalists, gourmet cuisine, and heated observation decks.',\n            exclusivity: 'Limited to 12 Guests',\n            price: 'Complimentary'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-gray-900 via-black to-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                            children: \"Exclusive Experiences\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gold-400 max-w-4xl mx-auto leading-relaxed\",\n                            children: \"Access to the world's most coveted destinations and experiences, available only to Octavia Opulence members\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full text-gold-400 text-sm font-semibold tracking-wider\",\n                                children: \"VALUED AT OVER $2M ANNUALLY\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: destinations.map((destination)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-gray-800 to-black rounded-none border border-gold-400/20 overflow-hidden hover:border-gold-400/50 transition-all duration-500 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-64 bg-gradient-to-br from-gold-400/20 to-purple-900/30 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/40\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 bg-gold-400 text-black px-3 py-1 rounded-none text-xs font-bold tracking-wider\",\n                                            children: destination.exclusivity\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-4 right-4 bg-black/70 text-gold-400 px-3 py-1 rounded-none text-xs font-semibold\",\n                                            children: destination.price\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/80 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 border-2 border-gold-400/50 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gold-400 text-2xl\",\n                                                            children: \"✈\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-light\",\n                                                        children: \"Ultra-Luxury Experience\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-4 text-white group-hover:text-gold-400 transition-colors\",\n                                            children: destination.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-6 leading-relaxed\",\n                                            children: destination.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: `/experiences/${destination.id}`,\n                                            className: \"inline-flex items-center text-gold-400 font-semibold hover:text-white transition-colors group\",\n                                            children: [\n                                                \"Reserve Experience\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, destination.id, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/exclusive-portfolio\",\n                        className: \"inline-flex items-center px-10 py-4 border-2 border-gold-400 text-gold-400 font-semibold rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300 group\",\n                        children: [\n                            \"View Complete Portfolio\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/FeaturedDestinations.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/FeaturedDestinations.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white pt-16 pb-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"QueerLuxe Travel Studio\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 9,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"Travel Authentically. Live Luxuriously. Belong Anywhere.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white hover:text-[#D4AF37]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Instagram\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 15,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                        lineNumber: 17,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 16,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 14,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white hover:text-[#D4AF37]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 21,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                        lineNumber: 23,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 22,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white hover:text-[#D4AF37]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Facebook\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 27,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                        lineNumber: 29,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white hover:text-[#D4AF37]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"LinkedIn\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                        lineNumber: 35,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold mb-4 text-[#D4AF37]\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Our Team\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Careers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Press\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold mb-4 text-[#D4AF37]\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Safety Information\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Travel Guides\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"FAQs\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Testimonials\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold mb-4 text-[#D4AF37]\",\n                                    children: \"Legal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Cookie Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-center\",\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" QueerLuxe Travel Studio. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/theplatform-1/src/components/Hero.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/HowItWorks.tsx":
/*!***************************************!*\
  !*** ./src/components/HowItWorks.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction HowItWorks() {\n    const steps = [\n        {\n            id: '1',\n            title: 'Discover',\n            description: 'Browse our curated collection of LGBTQ+ friendly luxury destinations and experiences.',\n            icon: '🔍'\n        },\n        {\n            id: '2',\n            title: 'Personalize',\n            description: 'Work with our travel experts to customize your perfect itinerary based on your preferences.',\n            icon: '✨'\n        },\n        {\n            id: '3',\n            title: 'Experience',\n            description: 'Enjoy your luxury travel experience with confidence, knowing every detail has been thoughtfully arranged.',\n            icon: '🌈'\n        },\n        {\n            id: '4',\n            title: 'Share',\n            description: 'Connect with the QueerLuxe community and share your experiences to inspire others.',\n            icon: '❤️'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-3xl md:text-4xl font-bold text-center mb-4 text-gray-800\",\n                    children: \"How It Works\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl text-center text-gray-600 mb-12\",\n                    children: \"Your journey to extraordinary LGBTQ+ travel experiences\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: steps.map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white text-lg mb-4\",\n                                    children: step.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2 text-gray-800\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, step.id, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/HowItWorks.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/HowItWorks.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/theplatform-1/src/components/Navigation.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Newsletter.tsx":
/*!***************************************!*\
  !*** ./src/components/Newsletter.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Newsletter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Newsletter() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gradient-to-r from-purple-600 to-pink-600 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                    children: \"Stay Updated\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mb-8 max-w-2xl mx-auto\",\n                    children: \"Subscribe to our newsletter for exclusive travel offers, LGBTQ+ friendly destination updates, and luxury travel inspiration.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"max-w-md mx-auto flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            placeholder: \"Your email address\",\n                            className: \"flex-grow px-4 py-3 rounded-lg text-gray-900 focus:outline-none\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-white text-purple-600 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors\",\n                            children: \"Subscribe\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-sm opacity-80\",\n                    children: \"We respect your privacy. Unsubscribe at any time.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/Newsletter.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Newsletter.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Testimonials() {\n    const testimonials = [\n        {\n            id: '1',\n            name: 'Alexandra Sterling',\n            title: 'Tech Entrepreneur',\n            location: 'Silicon Valley',\n            quote: 'Octavia Opulence has redefined what luxury means to me. The private island experience in the Maldives was beyond anything I could have imagined. Worth every penny of the membership.',\n            image: '/images/member-1.jpg',\n            memberSince: '2019'\n        },\n        {\n            id: '2',\n            name: 'Dr. Marcus Chen',\n            title: 'Investment Banker',\n            location: 'Hong Kong',\n            quote: 'The network alone justifies the membership cost. I\\'ve made invaluable connections and experienced destinations that simply aren\\'t available to the general public.',\n            image: '/images/member-2.jpg',\n            memberSince: '2020'\n        },\n        {\n            id: '3',\n            name: 'Riley Blackwood',\n            title: 'Fashion Designer',\n            location: 'London',\n            quote: 'The level of service and exclusivity is unmatched. From private museum tours to Michelin-starred chefs on demand, Octavia delivers experiences money can\\'t typically buy.',\n            image: '/images/member-3.jpg',\n            memberSince: '2018'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"Member Testimonials\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Hear from our exclusive community of discerning travelers\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block bg-gold-400/10 border border-gold-400/30 px-6 py-2 rounded-full text-gold-600 text-sm font-semibold tracking-wider\",\n                                children: \"VERIFIED MEMBERS ONLY\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-gray-50 to-white p-8 rounded-none border-l-4 border-gold-400 shadow-xl hover:shadow-2xl transition-all duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-xl\",\n                                                    children: testimonial.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gold-600 font-semibold text-sm\",\n                                                        children: testimonial.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: testimonial.location\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                        className: \"text-gray-700 italic text-lg leading-relaxed mb-6\",\n                                        children: [\n                                            '\"',\n                                            testimonial.quote,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gold-400 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: [\n                                                            \"Member since \",\n                                                            testimonial.memberSince\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex text-gold-400\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, i, false, {\n                                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, this)\n                        }, testimonial.id, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/Testimonials.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Testimonials.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ValueProposition.tsx":
/*!*********************************************!*\
  !*** ./src/components/ValueProposition.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ValueProposition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction ValueProposition() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                            children: \"Membership Privileges\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                            lineNumber: 6,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gold-400 max-w-3xl mx-auto\",\n                            children: \"Experience the pinnacle of luxury travel with benefits that money alone cannot buy\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                            lineNumber: 7,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-gray-900 to-black p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6 text-black\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-center mb-4 text-white\",\n                                    children: \"Private Jet Access\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-center leading-relaxed\",\n                                    children: \"Skip commercial flights entirely. Our fleet of private jets ensures complete privacy, luxury, and convenience for every journey.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-sm font-semibold tracking-wider\",\n                                        children: \"UNLIMITED FLIGHTS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-gray-900 to-black p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6 text-black\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-center mb-4 text-white\",\n                                    children: \"Exclusive Properties\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-center leading-relaxed\",\n                                    children: \"Access to private islands, historic castles, and ultra-luxury resorts unavailable to the public. Each property vetted for complete LGBTQ+ affirmation.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-sm font-semibold tracking-wider\",\n                                        children: \"200+ PRIVATE VENUES\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-gray-900 to-black p-8 rounded-none border border-gold-400/20 hover:border-gold-400/50 transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6 text-black\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-center mb-4 text-white\",\n                                    children: \"Elite Network\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-center leading-relaxed\",\n                                    children: \"Join an exclusive community of influential LGBTQ+ leaders, entrepreneurs, and visionaries. Networking events in the world's most prestigious locations.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-sm font-semibold tracking-wider\",\n                                        children: \"INVITATION ONLY\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/ValueProposition.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ValueProposition.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/ghost.ts":
/*!**************************!*\
  !*** ./src/lib/ghost.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getFeaturedPosts: () => (/* binding */ getFeaturedPosts),\n/* harmony export */   getPostBySlug: () => (/* binding */ getPostBySlug),\n/* harmony export */   getPosts: () => (/* binding */ getPosts),\n/* harmony export */   getPostsByTag: () => (/* binding */ getPostsByTag),\n/* harmony export */   getRecentPosts: () => (/* binding */ getRecentPosts)\n/* harmony export */ });\n/* harmony import */ var _tryghost_content_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tryghost/content-api */ \"(rsc)/./node_modules/@tryghost/content-api/es/content-api.js\");\n\n// Create API instance with site credentials\nconst api = new _tryghost_content_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    url: 'https://rainbow-millipede.pikapod.net',\n    key: 'feb948c4d47fa122219ac0e131',\n    version: 'v5.0'\n});\n// Helper function to get all posts\nasync function getPosts() {\n    try {\n        const posts = await api.posts.browse({\n            limit: 'all',\n            include: 'tags,authors',\n            order: 'published_at DESC',\n            formats: [\n                'html'\n            ]\n        });\n        return posts;\n    } catch (error) {\n        console.error('Error fetching posts:', error);\n        return [];\n    }\n}\n// Helper function to get a single post by slug\nasync function getPostBySlug(slug) {\n    try {\n        const post = await api.posts.read({\n            slug: slug\n        }, {\n            include: 'tags,authors'\n        });\n        return post;\n    } catch (error) {\n        console.error('Error fetching post:', error);\n        return null;\n    }\n}\n// Helper function to get featured posts\nasync function getFeaturedPosts(limit = 3) {\n    try {\n        const posts = await api.posts.browse({\n            limit: limit,\n            filter: 'featured:true',\n            include: 'tags,authors',\n            order: 'published_at DESC',\n            formats: [\n                'html'\n            ]\n        });\n        return posts;\n    } catch (error) {\n        console.error('Error fetching featured posts:', error);\n        return [];\n    }\n}\n// Helper function to get recent posts\nasync function getRecentPosts(limit = 6) {\n    try {\n        const posts = await api.posts.browse({\n            limit: limit,\n            include: 'tags,authors',\n            order: 'published_at DESC',\n            formats: [\n                'html'\n            ]\n        });\n        return posts;\n    } catch (error) {\n        console.error('Error fetching recent posts:', error);\n        return [];\n    }\n}\n// Helper function to get posts by tag\nasync function getPostsByTag(tagSlug, limit = 10) {\n    try {\n        const posts = await api.posts.browse({\n            limit: limit,\n            filter: `tag:${tagSlug}`,\n            include: 'tags,authors',\n            order: 'published_at DESC'\n        });\n        return posts;\n    } catch (error) {\n        console.error('Error fetching posts by tag:', error);\n        return [];\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ghost.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(ssr)/./src/components/Hero.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZhcHAtZGlyJTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmxldmloYW5raW5zJTJGdGhlcGxhdGZvcm0tMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGaW1hZ2UtY29tcG9uZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGc3JjJTJGY29tcG9uZW50cyUyRkhlcm8udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQW1IO0FBQ25IO0FBQ0Esc05BQXNIO0FBQ3RIO0FBQ0EsOEpBQTBIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbGV2aWhhbmtpbnMvdGhlcGxhdGZvcm0tMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hcHAtZGlyL2xpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9sZXZpaGFua2lucy90aGVwbGF0Zm9ybS0xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2ltYWdlLWNvbXBvbmVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9sZXZpaGFua2lucy90aGVwbGF0Zm9ybS0xL3NyYy9jb21wb25lbnRzL0hlcm8udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGV2aWhhbmtpbnMlMkZ0aGVwbGF0Zm9ybS0xJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsZXZpaGFua2lucyUyRnRoZXBsYXRmb3JtLTElMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvbGV2aWhhbmtpbnMvdGhlcGxhdGZvcm0tMS9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Hero() {\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const slides = [\n        {\n            title: \"OCTAVIA OPULENCE\",\n            subtitle: \"The World's Most Exclusive LGBTQ+ Travel Society\",\n            description: \"Join an elite circle of 500 discerning members who access the world's most extraordinary experiences. Private islands, Michelin-starred chefs, and bespoke adventures await.\",\n            cta: \"Apply for Membership\",\n            accent: \"Membership by Invitation Only • $25,000 Annually\"\n        },\n        {\n            title: \"ULTRA-PRIVATE ESCAPES\",\n            subtitle: \"Beyond First Class. Beyond Luxury.\",\n            description: \"Private jets, exclusive resorts, and experiences money can't buy. Our members enjoy access to the world's most coveted destinations with complete privacy and security.\",\n            cta: \"View Portfolio\",\n            accent: \"500 Members Worldwide • Unlimited Luxury\"\n        },\n        {\n            title: \"CONCIERGE PERFECTION\",\n            subtitle: \"Your Personal Travel Architect\",\n            description: \"24/7 dedicated concierge service. From securing impossible reservations to arranging private museum tours, we make the impossible, effortless.\",\n            cta: \"Schedule Consultation\",\n            accent: \"White-Glove Service • Global Network\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Hero.useEffect.timer\": ()=>{\n                    setCurrentSlide({\n                        \"Hero.useEffect.timer\": (prev)=>(prev + 1) % slides.length\n                    }[\"Hero.useEffect.timer\"]);\n                }\n            }[\"Hero.useEffect.timer\"], 6000);\n            return ({\n                \"Hero.useEffect\": ()=>clearInterval(timer)\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        slides.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900 via-black to-pink-900 opacity-90\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-20 w-32 h-32 border border-gold-400 rotate-45 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-32 right-32 w-24 h-24 border border-purple-400 rotate-12 animate-bounce\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full opacity-20 animate-ping\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gold-400 text-sm md:text-base font-light tracking-[0.3em] uppercase animate-fade-in-up opacity-90\",\n                            children: slides[currentSlide].accent\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight animate-fade-in-up animation-delay-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent\",\n                                children: slides[currentSlide].title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl md:text-3xl lg:text-4xl font-light text-purple-200 animate-fade-in-up animation-delay-400\",\n                            children: slides[currentSlide].subtitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-600\",\n                            children: slides[currentSlide].description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up animation-delay-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/membership\",\n                                className: \"group relative px-10 py-5 bg-gradient-to-r from-gold-400 to-gold-600 text-black font-bold rounded-none border-2 border-transparent hover:border-white transition-all duration-300 transform hover:scale-105 hover:shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: slides[currentSlide].cta\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-white to-gold-200 opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/private-consultation\",\n                                className: \"group px-10 py-5 border-2 border-gold-400 text-gold-400 font-semibold rounded-none hover:bg-gold-400 hover:text-black transition-all duration-300 transform hover:scale-105\",\n                                children: \"Private Consultation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 animate-fade-in-up animation-delay-1000\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center px-6 py-3 bg-black/50 border border-gold-400/30 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-gold-400 rounded-full mr-3 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-400 text-sm font-light tracking-wider\",\n                                    children: \"LIMITED TO 500 MEMBERS GLOBALLY\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-16 space-x-3\",\n                        children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>setCurrentSlide(index),\n                                \"aria-label\": `Go to slide ${index + 1}`,\n                                className: `w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-gold-400 scale-125' : 'bg-white/30 hover:bg-white/50'}`\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/Hero.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navigation() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"text-2xl font-bold text-purple-600\",\n                            children: \"QueerLuxe Travel Studio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/destinations\",\n                                    className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                    children: \"Destinations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/blog\",\n                                    className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                    children: \"Blog\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/book-consultation\",\n                                className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all\",\n                                children: \"Book Consultation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: toggleMenu,\n                            \"aria-label\": \"Toggle navigation menu\",\n                            className: \"md:hidden text-gray-700 hover:text-purple-600 focus:outline-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/destinations\",\n                                className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Destinations\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/blog\",\n                                className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Blog\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/contact\",\n                                className: \"text-gray-700 hover:text-purple-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/book-consultation\",\n                                className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full font-medium text-center hover:shadow-lg transition-all\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Book Consultation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/theplatform-1/src/components/Navigation.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/date-fns","vendor-chunks/@swc","vendor-chunks/@tryghost"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Flevihankins%2Ftheplatform-1%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flevihankins%2Ftheplatform-1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();