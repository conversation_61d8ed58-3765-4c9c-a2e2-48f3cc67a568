(()=>{var e={};e.id=974,e.ids=[974],e.modules={22:(e,r,o)=>{"use strict";o.r(r),o.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var n=o(5239),t=o(8088),s=o(8170),i=o.n(s),a=o(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);o.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,1204)),"/Users/<USER>/theplatform-1/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(o.bind(o,4431)),"/Users/<USER>/theplatform-1/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["/Users/<USER>/theplatform-1/src/app/page.tsx"],u={require:o,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>t});var n=o(7413);function t(){return(0,n.jsxs)("main",{className:"min-h-screen",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Hero'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ValueProposition'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/FeaturedDestinations'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Testimonials'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/HowItWorks'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/BlogPreview'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/Newsletter'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}!function(){var e=Error("Cannot find module '@/components/Hero'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ValueProposition'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/FeaturedDestinations'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Testimonials'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/HowItWorks'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/BlogPreview'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/Newsletter'");throw e.code="MODULE_NOT_FOUND",e}()},1272:(e,r,o)=>{Promise.resolve().then(o.t.bind(o,6346,23)),Promise.resolve().then(o.t.bind(o,7924,23)),Promise.resolve().then(o.t.bind(o,5656,23)),Promise.resolve().then(o.t.bind(o,99,23)),Promise.resolve().then(o.t.bind(o,8243,23)),Promise.resolve().then(o.t.bind(o,8827,23)),Promise.resolve().then(o.t.bind(o,2763,23)),Promise.resolve().then(o.t.bind(o,7173,23))},1467:()=>{},1544:(e,r,o)=>{Promise.resolve().then(o.t.bind(o,6444,23)),Promise.resolve().then(o.t.bind(o,6042,23)),Promise.resolve().then(o.t.bind(o,8170,23)),Promise.resolve().then(o.t.bind(o,9477,23)),Promise.resolve().then(o.t.bind(o,9345,23)),Promise.resolve().then(o.t.bind(o,2089,23)),Promise.resolve().then(o.t.bind(o,6577,23)),Promise.resolve().then(o.t.bind(o,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>a,metadata:()=>i});var n=o(7413),t=o(7339),s=o.n(t);o(1135);let i={title:"QueerLuxe Travel Studio",description:"Bespoke, luxurious travel by and for the LGBTQ+ community and allies"};function a({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:s().className,children:e})})}},6487:()=>{},7395:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),n=r.X(0,[435],()=>o(22));module.exports=n})();