#!/bin/bash

# Octavia Opulence AI Receptionist System Setup Script for macOS
# This script automates the complete setup of the Octavia AI receptionist system
# including all components: AI core, Twilio integration, persona framework, and web interface

# Text formatting
BOLD="\033[1m"
GREEN="\033[0;32m"
BLUE="\033[0;34m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

# Function to display section headers
section() {
  echo -e "\n${BOLD}${BLUE}==== $1 ====${NC}\n"
}

# Function to display success messages
success() {
  echo -e "${GREEN}✓ $1${NC}"
}

# Function to display info messages
info() {
  echo -e "${YELLOW}ℹ $1${NC}"
}

# Function to display error messages
error() {
  echo -e "${RED}✗ $1${NC}"
}

# Function to check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Function to check and install Homebrew packages
check_and_install_brew() {
  if ! brew list $1 &>/dev/null; then
    info "Installing $1..."
    brew install $1 > /dev/null 2>&1
    if [ $? -eq 0 ]; then
      success "$1 installed successfully"
    else
      error "Failed to install $1. Please install manually."
      exit 1
    fi
  else
    success "$1 is already installed"
  fi
}

# Function to create directory if it doesn't exist
create_dir_if_not_exists() {
  if [ ! -d "$1" ]; then
    mkdir -p "$1"
    success "Created directory: $1"
  else
    info "Directory already exists: $1"
  fi
}

# Check macOS version
check_macos_version() {
  os_version=$(sw_vers -productVersion)
  required_version="10.15"
  
  if [[ "$(printf '%s\n' "$required_version" "$os_version" | sort -V | head -n1)" != "$required_version" ]]; then
    error "This script requires macOS Catalina (10.15) or newer. You are running macOS $os_version."
    exit 1
  else
    success "macOS version $os_version is compatible"
  fi
}

# Check if script is run with sudo
check_sudo() {
  if [ "$EUID" -ne 0 ]; then
    error "Please run this script with sudo"
    exit 1
  fi
}

# Welcome message
clear
echo -e "${BOLD}${BLUE}"
echo "  ___       _             _         ___            _                      "
echo " / _ \ _ __| |_ __ ___   (_) __ _  / _ \ _ __  _  | | ___ _ __   ___ ___ "
echo "| | | | '__| __/ _\` \ \ / / / _\` || | | | '_ \| | | |/ _ \ '_ \ / __/ _ \\"
echo "| |_| | |  | || (_| |\ V /| | (_| || |_| | |_) | |_| |  __/ | | | (_|  __/"
echo " \___/|_|   \__\__,_| \_/ |_|\__,_| \___/| .__/ \__, |\___|_| |_|\___\___|"
echo "                                         |_|    |___/                     "
echo -e "${NC}"
echo -e "${BOLD}AI Receptionist System Setup Script for macOS${NC}"
echo -e "This script will set up the complete Octavia Opulence AI receptionist system\n"

# Check macOS version
section "Checking System Compatibility"
check_macos_version

# Check if script is run with sudo
check_sudo

# Confirm before proceeding
read -p "Do you want to proceed with the installation? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  info "Installation cancelled"
  exit 0
fi

# Set installation directory
INSTALL_DIR="/usr/local/opt/octavia-ai"
CONFIG_DIR="$INSTALL_DIR/config"
DATA_DIR="$INSTALL_DIR/data"
LOG_DIR="$HOME/Library/Logs/octavia-ai"
ENV_FILE="$CONFIG_DIR/.env"

# Create installation directories
section "Creating Directory Structure"
create_dir_if_not_exists "$INSTALL_DIR"
create_dir_if_not_exists "$CONFIG_DIR"
create_dir_if_not_exists "$DATA_DIR"
create_dir_if_not_exists "$DATA_DIR/personas"
create_dir_if_not_exists "$DATA_DIR/audio"
create_dir_if_not_exists "$DATA_DIR/models"
create_dir_if_not_exists "$LOG_DIR"

# Check for Homebrew and install if needed
section "Checking for Homebrew"
if ! command_exists brew; then
  info "Installing Homebrew..."
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
  success "Homebrew installed"
else
  success "Homebrew is already installed"
  info "Updating Homebrew..."
  brew update > /dev/null 2>&1
  success "Homebrew updated"
fi

# Install system dependencies
section "Installing System Dependencies"
DEPENDENCIES=("python@3.11" "node@20" "mongodb-community" "redis" "nginx")
for dep in "${DEPENDENCIES[@]}"; do
  check_and_install_brew $dep
done

# Install certbot
info "Installing certbot..."
brew install certbot > /dev/null 2>&1
success "Certbot installed"

# Set up Python virtual environment
section "Setting Up Python Environment"
info "Creating Python virtual environment..."
/usr/local/opt/python@3.11/bin/python3 -m venv "$INSTALL_DIR/venv"
success "Python virtual environment created"

# Activate virtual environment and install Python packages
info "Installing Python packages..."
source "$INSTALL_DIR/venv/bin/activate"
pip install --upgrade pip > /dev/null 2>&1
pip install twilio flask gunicorn numpy pandas scikit-learn tensorflow transformers pymongo redis pydub > /dev/null 2>&1
success "Python packages installed"

# Install Node.js packages
section "Setting Up Node.js Environment"
info "Installing Node.js packages..."
npm install -g pm2 > /dev/null 2>&1
mkdir -p "$INSTALL_DIR/web"
cat > "$INSTALL_DIR/web/package.json" << EOF
{
  "name": "octavia-ai-receptionist",
  "version": "1.0.0",
  "description": "Octavia Opulence AI Receptionist System",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "express": "^4.17.1",
    "socket.io": "^4.1.2",
    "twilio": "^3.67.0",
    "dotenv": "^10.0.0",
    "mongoose": "^6.0.12",
    "cors": "^2.8.5",
    "body-parser": "^1.19.0",
    "axios": "^0.24.0",
    "winston": "^3.3.3"
  }
}
EOF
cd "$INSTALL_DIR/web" && npm install > /dev/null 2>&1
success "Node.js packages installed"

# Create configuration files
section "Creating Configuration Files"

# Create .env file
info "Creating environment configuration..."
cat > "$ENV_FILE" << EOF
# Octavia AI Receptionist System Environment Configuration

# Application Settings
APP_NAME=Octavia AI Receptionist
APP_ENV=production
APP_PORT=3000
APP_URL=https://ai.octaviaopulence.com

# Database Settings
MONGODB_URI=mongodb://localhost:27017/octavia_ai
REDIS_URL=redis://localhost:6379

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=(*************

# AI Model Settings
AI_MODEL_PATH=/usr/local/opt/octavia-ai/data/models
PERSONA_DATA_PATH=/usr/local/opt/octavia-ai/data/personas

# Logging
LOG_LEVEL=info
LOG_PATH=$HOME/Library/Logs/octavia-ai

# Security
JWT_SECRET=change_this_to_a_secure_random_string
SESSION_SECRET=change_this_to_another_secure_random_string
EOF
success "Environment configuration created"

# Create Nginx configuration
info "Creating Nginx configuration..."
mkdir -p /usr/local/etc/nginx/servers
cat > "/usr/local/etc/nginx/servers/octavia-ai.conf" << EOF
server {
    listen 80;
    server_name ai.octaviaopulence.com localhost;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
success "Nginx configuration created"

# Create launchd service files
section "Creating Launch Agents"

# Create MongoDB launch agent
info "Creating MongoDB launch agent..."
cat > "$HOME/Library/LaunchAgents/com.octavia.mongodb.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.octavia.mongodb</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/opt/mongodb-community/bin/mongod</string>
        <string>--config</string>
        <string>/usr/local/etc/mongod.conf</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardErrorPath</key>
    <string>${LOG_DIR}/mongodb.err</string>
    <key>StandardOutPath</key>
    <string>${LOG_DIR}/mongodb.log</string>
</dict>
</plist>
EOF
success "MongoDB launch agent created"

# Create Redis launch agent
info "Creating Redis launch agent..."
cat > "$HOME/Library/LaunchAgents/com.octavia.redis.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.octavia.redis</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/opt/redis/bin/redis-server</string>
        <string>/usr/local/etc/redis.conf</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardErrorPath</key>
    <string>${LOG_DIR}/redis.err</string>
    <key>StandardOutPath</key>
    <string>${LOG_DIR}/redis.log</string>
</dict>
</plist>
EOF
success "Redis launch agent created"

# Create Octavia AI launch agent
info "Creating Octavia AI launch agent..."
cat > "$HOME/Library/LaunchAgents/com.octavia.ai.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.octavia.ai</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/node</string>
        <string>${INSTALL_DIR}/web/server.js</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>WorkingDirectory</key>
    <string>${INSTALL_DIR}/web</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:${INSTALL_DIR}/venv/bin</string>
        <key>NODE_ENV</key>
        <string>production</string>
    </dict>
    <key>StandardErrorPath</key>
    <string>${LOG_DIR}/octavia-ai.err</string>
    <key>StandardOutPath</key>
    <string>${LOG_DIR}/octavia-ai.log</string>
</dict>
</plist>
EOF
success "Octavia AI launch agent created"

# Create AI core components
section "Setting Up AI Core Components"

# Create server.js for the web interface
info "Creating web server..."
cat > "$INSTALL_DIR/web/server.js" << EOF
// Octavia AI Receptionist Web Server
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const bodyParser = require('body-parser');
const cors = require('cors');
const twilio = require('twilio');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const winston = require('winston');

// Load environment variables
dotenv.config({ path: '../config/.env' });

// Initialize logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: path.join(process.env.LOG_PATH, 'error.log'), level: 'error' }),
    new winston.transports.File({ filename: path.join(process.env.LOG_PATH, 'combined.log') })
  ]
});

if (process.env.APP_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Twilio configuration
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Twilio webhook for incoming voice calls
app.post('/api/twilio/voice/inbound', (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();
  
  // Example response - this would be replaced with AI logic
  twiml.say({
    voice: 'Polly.Joanna-Neural',
    language: 'en-US'
  }, 'Welcome to Octavia Opulence. Our AI receptionist is being configured. Please call back later.');
  
  res.type('text/xml');
  res.send(twiml.toString());
  
  logger.info('Received inbound call', { 
    from: req.body.From,
    to: req.body.To,
    callSid: req.body.CallSid
  });
});

// Twilio webhook for incoming SMS
app.post('/api/twilio/sms/inbound', (req, res) => {
  const twiml = new twilio.twiml.MessagingResponse();
  
  // Example response - this would be replaced with AI logic
  twiml.message('Thank you for contacting Octavia Opulence. Our AI receptionist is being configured. Please try again later.');
  
  res.type('text/xml');
  res.send(twiml.toString());
  
  logger.info('Received inbound SMS', { 
    from: req.body.From,
    to: req.body.To,
    body: req.body.Body
  });
});

// Socket.IO connection
io.on('connection', (socket) => {
  logger.info('New client connected');
  
  socket.on('disconnect', () => {
    logger.info('Client disconnected');
  });
});

// Start server
const PORT = process.env.APP_PORT || 3000;
server.listen(PORT, () => {
  logger.info(\`Octavia AI Receptionist server running on port \${PORT}\`);
});
EOF
success "Web server created"

# Create AI persona data files
info "Creating AI persona data files..."

# Create The Grand Dame persona data
cat > "$DATA_DIR/personas/grand_dame.json" << EOF
{
  "id": "grand_dame",
  "name": "The Grand Dame",
  "description": "Elegant, theatrical, with a flair for storytelling and historical context",
  "voice": {
    "pitch": "medium-low",
    "pace": "measured",
    "articulation": "precise",
    "modulation": "expressive",
    "accent": "trans-atlantic"
  },
  "language": {
    "sentence_structure": "complex",
    "vocabulary_level": "advanced",
    "filler_phrases": ["My dear", "One might say", "Indeed"],
    "signature_sounds": ["soft chuckle"]
  },
  "activation_triggers": [
    "historical context",
    "cultural information",
    "brand story",
    "first-time users",
    "dramatic reassurance",
    "luxury positioning"
  ],
  "superpowers": {
    "primary": "empathy_amplifier",
    "secondary": "communication_clarity"
  },
  "visual_theme": {
    "primary_color": "#4A2545",
    "secondary_color": "#CFB53B",
    "accent_color": "#FFFFF0",
    "icon": "vintage_key"
  },
  "audio_theme": {
    "background": "classical_piano",
    "transition_sound": "harp_glissando",
    "confirmation_sound": "warm_bell"
  }
}
EOF

# Create The Sage of Journeys persona data
cat > "$DATA_DIR/personas/sage_of_journeys.json" << EOF
{
  "id": "sage_of_journeys",
  "name": "The Sage of Journeys",
  "description": "Calm, knowledgeable, with deep travel wisdom and practical guidance",
  "voice": {
    "pitch": "medium",
    "pace": "even",
    "articulation": "clear",
    "modulation": "calm",
    "accent": "neutral"
  },
  "language": {
    "sentence_structure": "logical",
    "vocabulary_level": "informative",
    "filler_phrases": ["Consider this", "In my experience", "Notably"],
    "signature_sounds": ["thoughtful hum"]
  },
  "activation_triggers": [
    "travel logistics",
    "safety concerns",
    "destination information",
    "itinerary planning",
    "travel documentation",
    "troubleshooting"
  ],
  "superpowers": {
    "primary": "global_perspective",
    "secondary": "time_freeze"
  },
  "visual_theme": {
    "primary_color": "#336B87",
    "secondary_color": "#B2BEB5",
    "accent_color": "#F9F6EE",
    "icon": "compass"
  },
  "audio_theme": {
    "background": "ambient_tones",
    "transition_sound": "compass_click",
    "confirmation_sound": "positive_sequence"
  }
}
EOF

# Create The Spark of Connection persona data
cat > "$DATA_DIR/personas/spark_of_connection.json" << EOF
{
  "id": "spark_of_connection",
  "name": "The Spark of Connection",
  "description": "Energetic, direct, with a talent for building relationships and quick action",
  "voice": {
    "pitch": "medium-high",
    "pace": "brisk",
    "articulation": "conversational",
    "modulation": "dynamic",
    "accent": "warm"
  },
  "language": {
    "sentence_structure": "direct",
    "vocabulary_level": "accessible",
    "filler_phrases": ["Let's connect", "Absolutely", "Fantastic"],
    "signature_sounds": ["bright affirmation"]
  },
  "activation_triggers": [
    "community experiences",
    "group travel",
    "networking events",
    "urgent assistance",
    "personal recommendations",
    "social impact"
  ],
  "superpowers": {
    "primary": "teleportation",
    "secondary": "inclusive_innovation"
  },
  "visual_theme": {
    "primary_color": "#FF6B6B",
    "secondary_color": "#4ECDC4",
    "accent_color": "#FFFAF0",
    "icon": "network_nodes"
  },
  "audio_theme": {
    "background": "upbeat_rhythm",
    "transition_sound": "connection_tone",
    "confirmation_sound": "cheerful_chime"
  }
}
EOF

# Create The Connoisseur of Joys persona data
cat > "$DATA_DIR/personas/connoisseur_of_joys.json" << EOF
{
  "id": "connoisseur_of_joys",
  "name": "The Connoisseur of Joys",
  "description": "Sophisticated Gay Male archetype with refined taste and appreciation for sensory experiences",
  "voice": {
    "pitch": "medium-low",
    "pace": "unhurried",
    "articulation": "precise",
    "modulation": "smooth",
    "accent": "refined"
  },
  "language": {
    "sentence_structure": "elegant",
    "vocabulary_level": "sensory",
    "filler_phrases": ["My dear", "Simply divine", "Exquisite"],
    "signature_sounds": ["appreciative hum"]
  },
  "activation_triggers": [
    "dining experiences",
    "aesthetic discussions",
    "unique experiences",
    "luxury shopping",
    "wellness inquiries",
    "cultural refinement"
  ],
  "superpowers": {
    "primary": "inclusive_innovation",
    "secondary": "global_perspective"
  },
  "visual_theme": {
    "primary_color": "#800020",
    "secondary_color": "#046307",
    "accent_color": "#DAA520",
    "icon": "wine_glass"
  },
  "audio_theme": {
    "background": "soft_jazz",
    "transition_sound": "jazz_chord",
    "confirmation_sound": "crystal_chime"
  }
}
EOF

# Create facet selection logic file
cat > "$DATA_DIR/personas/facet_selection_logic.json" << EOF
{
  "selection_weights": {
    "topic_relevance": 0.4,
    "user_history": 0.3,
    "emotional_state": 0.2,
    "time_sensitivity": 0.1
  },
  "transition_rules": {
    "minimum_turns_before_transition": 3,
    "maximum_transitions_per_conversation": 2,
    "required_confidence_threshold": 0.7
  },
  "conflict_resolution": {
    "primary_trigger_weight": 0.6,
    "secondary_trigger_weight": 0.3,
    "user_preference_weight": 0.1
  }
}
EOF

success "AI persona data files created"

# Create basic HTML interface
info "Creating web interface..."
mkdir -p "$INSTALL_DIR/web/public"
cat > "$INSTALL_DIR/web/public/index.html" << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octavia Opulence AI Receptionist</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Montserrat:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00585E;
            --gold-color: #D4AF37;
            --cream-color: #F5F5DC;
            --dark-color: #333333;
            --light-color: #FFFFFF;
            
            /* Facet-specific colors */
            --grand-dame-primary: #4A2545;
            --sage-journeys-primary: #336B87;
            --spark-connection-primary: #FF6B6B;
            --connoisseur-primary: #800020;
        }
        
        body {
            font-family: 'Montserrat', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--cream-color);
            color: var(--dark-color);
        }
        
        header {
            background-color: var(--primary-color);
            color: var(--light-color);
            padding: 1rem;
            text-align: center;
        }
        
        h1, h2, h3 {
            font-family: 'Playfair Display', serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: var(--gold-color);
        }
        
        .facet-selector {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .facet-button {
            padding: 1rem;
            margin: 0 0.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Montserrat', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .facet-button.grand-dame {
            background-color: var(--grand-dame-primary);
            color: var(--light-color);
        }
        
        .facet-button.sage-journeys {
            background-color: var(--sage-journeys-primary);
            color: var(--light-color);
        }
        
        .facet-button.spark-connection {
            background-color: var(--spark-connection-primary);
            color: var(--light-color);
        }
        
        .facet-button.connoisseur {
            background-color: var(--connoisseur-primary);
            color: var(--light-color);
        }
        
        .chat-container {
            background-color: var(--light-color);
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            height: 400px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .user-message {
            background-color: #E8E8E8;
            align-self: flex-end;
        }
        
        .ai-message {
            background-color: var(--primary-color);
            color: var(--light-color);
            align-self: flex-start;
        }
        
        .ai-message.grand-dame {
            background-color: var(--grand-dame-primary);
        }
        
        .ai-message.sage-journeys {
            background-color: var(--sage-journeys-primary);
        }
        
        .ai-message.spark-connection {
            background-color: var(--spark-connection-primary);
        }
        
        .ai-message.connoisseur {
            background-color: var(--connoisseur-primary);
        }
        
        .chat-input {
            display: flex;
            padding: 1rem;
            border-top: 1px solid #E8E8E8;
        }
        
        .chat-input input {
            flex-grow: 1;
            padding: 0.75rem;
            border: 1px solid #E8E8E8;
            border-radius: 5px;
            margin-right: 0.5rem;
        }
        
        .chat-input button {
            background-color: var(--gold-color);
            color: var(--dark-color);
            border: none;
            border-radius: 5px;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            font-weight: 500;
        }
        
        .status {
            text-align: center;
            margin: 1rem 0;
            font-style: italic;
            color: #666;
        }
        
        footer {
            background-color: var(--primary-color);
            color: var(--light-color);
            text-align: center;
            padding: 1rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">Octavia Opulence</div>
        <h1>AI Receptionist System</h1>
    </header>
    
    <div class="container">
        <div class="status">System Status: Setting Up</div>
        
        <div class="facet-selector">
            <button class="facet-button grand-dame">The Grand Dame</button>
            <button class="facet-button sage-journeys">The Sage of Journeys</button>
            <button class="facet-button spark-connection">The Spark of Connection</button>
            <button class="facet-button connoisseur">The Connoisseur of Joys</button>
        </div>
        
        <div class="chat-container">
            <div class="chat-messages">
                <div class="message ai-message grand-dame">
                    Welcome to Octavia Opulence, my dear. I'm delighted to make your acquaintance. Our AI receptionist system is currently being configured. Please do check back soon for a fully operational experience.
                </div>
            </div>
            
            <div class="chat-input">
                <input type="text" placeholder="Type your message here..." disabled>
                <button disabled>Send</button>
            </div>
        </div>
        
        <div class="status">
            <p>This is a placeholder interface for the Octavia Opulence AI Receptionist System.</p>
            <p>The complete system is being set up and will be available soon.</p>
        </div>
    </div>
    
    <footer>
        <p>&copy; 2025 Octavia Opulence. All rights reserved.</p>
    </footer>
    
    <script src="/socket.io/socket.io.js"></script>
    <script>
        // This is a placeholder for the actual functionality
        // The real implementation would connect to the server and handle interactions
        document.addEventListener('DOMContentLoaded', () => {
            const status = document.querySelector('.status');
            status.textContent = 'System Status: Configuration In Progress';
            
            // Facet button click handlers
            const facetButtons = document.querySelectorAll('.facet-button');
            facetButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const facetClass = button.classList[1];
                    const aiMessage = document.querySelector('.ai-message');
                    
                    // Remove all facet classes
                    aiMessage.classList.remove('grand-dame', 'sage-journeys', 'spark-connection', 'connoisseur');
                    
                    // Add the selected facet class
                    aiMessage.classList.add(facetClass);
                    
                    // Update message based on facet
                    switch(facetClass) {
                        case 'grand-dame':
                            aiMessage.textContent = 'Welcome to Octavia Opulence, my dear. I'm delighted to make your acquaintance. Our AI receptionist system is currently being configured. Please do check back soon for a fully operational experience.';
                            break;
                        case 'sage-journeys':
                            aiMessage.textContent = 'Welcome to Octavia Opulence. I'm here to provide you with practical travel guidance. Our AI receptionist system is currently being configured. Please check back soon for complete assistance with your journey planning.';
                            break;
                        case 'spark-connection':
                            aiMessage.textContent = 'Hi there! Welcome to Octavia Opulence! I'm excited to connect with you and help you build amazing travel experiences. Our AI system is just getting set up, so please come back soon!';
                            break;
                        case 'connoisseur':
                            aiMessage.textContent = 'Ah, welcome to Octavia Opulence, my dear. What exquisite timing. Our AI receptionist system is being artfully configured to provide you with the most refined travel experiences. Do return soon to indulge in our complete service.';
                            break;
                    }
                });
            });
        });
    </script>
</body>
</html>
EOF
success "Web interface created"

# Create Twilio integration script
info "Creating Twilio integration script..."
mkdir -p "$INSTALL_DIR/scripts"
cat > "$INSTALL_DIR/scripts/configure_twilio.py" << EOF
#!/usr/bin/env python3
"""
Twilio Configuration Script for Octavia AI Receptionist
This script configures the Twilio phone number with the appropriate webhooks
"""

import os
import sys
import argparse
from twilio.rest import Client
from dotenv import load_dotenv

# Load environment variables
sys.path.append('/usr/local/opt/octavia-ai')
load_dotenv('/usr/local/opt/octavia-ai/config/.env')

def configure_twilio(base_url):
    """Configure Twilio phone number with webhooks"""
    
    # Get Twilio credentials from environment
    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
    phone_number = os.environ.get('TWILIO_PHONE_NUMBER')
    
    if not all([account_sid, auth_token, phone_number]):
        print("Error: Twilio credentials not found in environment variables")
        return False
    
    try:
        # Initialize Twilio client
        client = Client(account_sid, auth_token)
        
        # Format phone number for API
        if phone_number.startswith('+'):
            formatted_phone = phone_number
        else:
            # Remove any non-digit characters and add +1 prefix if needed
            digits = ''.join(filter(str.isdigit, phone_number))
            if len(digits) == 10:
                formatted_phone = f"+1{digits}"
            else:
                formatted_phone = f"+{digits}"
        
        # Find the phone number in the account
        incoming_phone_numbers = client.incoming_phone_numbers.list(
            phone_number=formatted_phone
        )
        
        if not incoming_phone_numbers:
            print(f"Error: Phone number {phone_number} not found in Twilio account")
            return False
        
        # Get the first matching phone number
        number = incoming_phone_numbers[0]
        
        # Update the phone number with webhooks
        number.update(
            voice_url=f"{base_url}/api/twilio/voice/inbound",
            voice_method='POST',
            status_callback=f"{base_url}/api/twilio/voice/status",
            status_callback_method='POST',
            sms_url=f"{base_url}/api/twilio/sms/inbound",
            sms_method='POST',
            sms_status_callback=f"{base_url}/api/twilio/sms/status",
            sms_status_callback_method='POST'
        )
        
        print(f"Successfully configured Twilio phone number {phone_number} with webhooks")
        print(f"Voice webhook: {base_url}/api/twilio/voice/inbound")
        print(f"SMS webhook: {base_url}/api/twilio/sms/inbound")
        return True
        
    except Exception as e:
        print(f"Error configuring Twilio: {str(e)}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Configure Twilio for Octavia AI Receptionist')
    parser.add_argument('--url', required=True, help='Base URL for webhooks (e.g., https://ai.octaviaopulence.com)')
    
    args = parser.parse_args()
    configure_twilio(args.url)
EOF
chmod +x "$INSTALL_DIR/scripts/configure_twilio.py"
success "Twilio integration script created"

# Create AI persona initialization script
info "Creating AI persona initialization script..."
cat > "$INSTALL_DIR/scripts/initialize_personas.py" << EOF
#!/usr/bin/env python3
"""
AI Persona Initialization Script for Octavia AI Receptionist
This script loads and initializes the AI persona data
"""

import os
import sys
import json
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.expanduser('~/Library/Logs/octavia-ai/personas.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('persona_initializer')

def load_persona_data(data_dir):
    """Load all persona data from JSON files"""
    persona_dir = Path(data_dir) / 'personas'
    personas = {}
    
    try:
        # Load each persona file
        for persona_file in persona_dir.glob('*.json'):
            if persona_file.name == 'facet_selection_logic.json':
                continue
                
            with open(persona_file, 'r') as f:
                persona_data = json.load(f)
                persona_id = persona_data.get('id')
                if persona_id:
                    personas[persona_id] = persona_data
                    logger.info(f"Loaded persona: {persona_id}")
                else:
                    logger.warning(f"Skipping persona file without ID: {persona_file}")
        
        # Load facet selection logic
        selection_logic_file = persona_dir / 'facet_selection_logic.json'
        if selection_logic_file.exists():
            with open(selection_logic_file, 'r') as f:
                selection_logic = json.load(f)
                logger.info("Loaded facet selection logic")
        else:
            selection_logic = {}
            logger.warning("Facet selection logic file not found")
        
        return {
            'personas': personas,
            'selection_logic': selection_logic
        }
        
    except Exception as e:
        logger.error(f"Error loading persona data: {str(e)}")
        return None

def initialize_personas(data_dir):
    """Initialize the AI personas"""
    logger.info("Initializing AI personas...")
    
    # Load persona data
    persona_data = load_persona_data(data_dir)
    if not persona_data:
        logger.error("Failed to load persona data")
        return False
    
    # Log the loaded personas
    personas = persona_data['personas']
    logger.info(f"Loaded {len(personas)} personas: {', '.join(personas.keys())}")
    
    # Validate required personas
    required_personas = ['grand_dame', 'sage_of_journeys', 'spark_of_connection', 'connoisseur_of_joys']
    missing_personas = [p for p in required_personas if p not in personas]
    
    if missing_personas:
        logger.warning(f"Missing required personas: {', '.join(missing_personas)}")
    
    # Create a summary file
    try:
        summary_file = Path(data_dir) / 'personas' / 'summary.txt'
        with open(summary_file, 'w') as f:
            f.write("Octavia AI Receptionist - Persona Summary\n")
            f.write("=========================================\n\n")
            
            for persona_id, persona in personas.items():
                f.write(f"Persona: {persona.get('name', persona_id)}\n")
                f.write(f"Description: {persona.get('description', 'No description')}\n")
                f.write(f"Primary Superpower: {persona.get('superpowers', {}).get('primary', 'None')}\n")
                f.write(f"Secondary Superpower: {persona.get('superpowers', {}).get('secondary', 'None')}\n")
                f.write("\nActivation Triggers:\n")
                for trigger in persona.get('activation_triggers', []):
                    f.write(f"- {trigger}\n")
                f.write("\n" + "-"*50 + "\n\n")
        
        logger.info(f"Created persona summary at {summary_file}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating persona summary: {str(e)}")
        return False

if __name__ == "__main__":
    data_dir = os.environ.get('PERSONA_DATA_PATH', '/usr/local/opt/octavia-ai/data')
    initialize_personas(data_dir)
EOF
chmod +x "$INSTALL_DIR/scripts/initialize_personas.py"
success "AI persona initialization script created"

# Create startup script
info "Creating startup script..."
cat > "$INSTALL_DIR/start.sh" << EOF
#!/bin/bash

# Octavia AI Receptionist Startup Script for macOS

# Load environment variables
source /usr/local/opt/octavia-ai/config/.env

# Activate Python virtual environment
source /usr/local/opt/octavia-ai/venv/bin/activate

# Initialize AI personas
python /usr/local/opt/octavia-ai/scripts/initialize_personas.py

# Start services
brew services start mongodb-community
brew services start redis
brew services start nginx

# Start the web server
cd /usr/local/opt/octavia-ai/web
node server.js &

echo "Octavia AI Receptionist system started"
echo "Web interface available at: http://localhost:\${APP_PORT:-3000}"
EOF
chmod +x "$INSTALL_DIR/start.sh"
success "Startup script created"

# Create setup completion script
info "Creating setup completion script..."
cat > "$INSTALL_DIR/setup_complete.sh" << EOF
#!/bin/bash

# Octavia AI Receptionist Setup Completion Script for macOS

# Set proper permissions
chown -R $(whoami):admin /usr/local/opt/octavia-ai
chmod -R 755 /usr/local/opt/octavia-ai

# Load launch agents
launchctl load ~/Library/LaunchAgents/com.octavia.mongodb.plist
launchctl load ~/Library/LaunchAgents/com.octavia.redis.plist
launchctl load ~/Library/LaunchAgents/com.octavia.ai.plist

# Start services
brew services start mongodb-community
brew services start redis
brew services start nginx

# Display setup completion message
echo ""
echo "======================================================"
echo "Octavia AI Receptionist Setup Complete!"
echo "======================================================"
echo ""
echo "Next steps:"
echo ""
echo "1. Update Twilio credentials in /usr/local/opt/octavia-ai/config/.env"
echo "2. Configure your domain (ai.octaviaopulence.com) to point to this server"
echo "3. Set up SSL with: sudo certbot --nginx -d ai.octaviaopulence.com"
echo "4. Configure Twilio webhooks with: python /usr/local/opt/octavia-ai/scripts/configure_twilio.py --url https://ai.octaviaopulence.com"
echo "5. Access the web interface at: https://ai.octaviaopulence.com"
echo ""
echo "For local testing, you can access the web interface at: http://localhost:3000"
echo ""
echo "To use ngrok for local webhook testing:"
echo "1. Install ngrok: brew install ngrok"
echo "2. Run: ngrok http 3000"
echo "3. Use the ngrok URL for Twilio webhook configuration"
echo ""
echo "======================================================"
EOF
chmod +x "$INSTALL_DIR/setup_complete.sh"
success "Setup completion script created"

# Final setup steps
section "Completing Setup"

# Initialize AI personas
info "Initializing AI personas..."
source "$INSTALL_DIR/venv/bin/activate"
python "$INSTALL_DIR/scripts/initialize_personas.py"
success "AI personas initialized"

# Set proper permissions
info "Setting proper permissions..."
chown -R $(whoami):admin "$INSTALL_DIR"
chmod -R 755 "$INSTALL_DIR"
success "Permissions set"

# Create launch agent directory if it doesn't exist
if [ ! -d "$HOME/Library/LaunchAgents" ]; then
  mkdir -p "$HOME/Library/LaunchAgents"
fi

# Load launch agents
info "Loading launch agents..."
launchctl load ~/Library/LaunchAgents/com.octavia.mongodb.plist
launchctl load ~/Library/LaunchAgents/com.octavia.redis.plist
launchctl load ~/Library/LaunchAgents/com.octavia.ai.plist
success "Launch agents loaded"

# Start services
info "Starting services..."
brew services start mongodb-community
brew services start redis
brew services start nginx
success "Services started"

# Final message
section "Setup Complete"
echo -e "${GREEN}Octavia AI Receptionist system has been successfully set up on macOS!${NC}"
echo
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Update Twilio credentials in $ENV_FILE"
echo "2. Configure your domain (ai.octaviaopulence.com) to point to this server"
echo "3. Set up SSL with: sudo certbot --nginx -d ai.octaviaopulence.com"
echo "4. Configure Twilio webhooks with: python $INSTALL_DIR/scripts/configure_twilio.py --url https://ai.octaviaopulence.com"
echo "5. Access the web interface at: https://ai.octaviaopulence.com"
echo
echo -e "${YELLOW}For local testing:${NC}"
echo "Access the web interface at: http://localhost:3000"
echo
echo -e "${YELLOW}For local webhook testing:${NC}"
echo "1. Install ngrok: brew install ngrok"
echo "2. Run: ngrok http 3000"
echo "3. Use the ngrok URL for Twilio webhook configuration"
echo
echo -e "${BOLD}${BLUE}Thank you for using the Octavia Opulence AI Receptionist Setup Script for macOS!${NC}"
