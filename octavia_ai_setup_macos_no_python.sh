#!/bin/bash

# Octavia Opulence AI Receptionist System Setup Script for macOS
# Modified version that skips Python installation

# Text formatting
BOLD='\033[1m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print banner
echo -e "${BLUE}__       _             _         ___            _                      "
echo -e " / _ \\ _ __| |_ __ ___   (_) __ _  / _ \\ _ __  _  | | ___ _ __   ___ ___ "
echo -e "| | | | '__| __/ _\` \\ \\ / / / _\` || | | | '_ \\| | | |/ _ \\ '_ \\ / __/ _ \\"
echo -e "| |_| | |  | || (_| |\\ V /| | (_| || |_| | |_) | |_| |  __/ | | | (_|  __/"
echo -e " \\___/|_|   \\__\\__,_| \\_/ |_|\\__,_| \\___/| .__/ \\__, |\\___|_| |_|\\___\\___|"
echo -e "                                         |_|    |___/                     ${NC}"
echo ""
echo -e "${BOLD}AI Receptionist System Setup Script for macOS${NC}"
echo -e "This script will set up the complete Octavia Opulence AI receptionist system"
echo -e "Modified version: Skips Python installation step"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to create directory if it doesn't exist
create_directory() {
    if [ ! -d "$1" ]; then
        mkdir -p "$1"
        echo -e "✓ Created directory: $1"
    else
        echo -e "ℹ Directory already exists: $1"
    fi
}

# Check macOS version
echo -e "${BOLD}==== Checking System Compatibility ====${NC}"
echo ""

OS_VERSION=$(sw_vers -productVersion)
if [[ $(echo "$OS_VERSION" | cut -d. -f1) -ge 10 ]]; then
    echo -e "${GREEN}✓${NC} macOS version $OS_VERSION is compatible"
else
    echo -e "${RED}✗${NC} macOS version $OS_VERSION is not compatible. Minimum required: 10.15"
    exit 1
fi

# Ask for confirmation
read -p "Do you want to proceed with the installation? (y/n): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Installation cancelled."
    exit 0
fi

# Create directory structure
echo ""
echo -e "${BOLD}==== Creating Directory Structure ====${NC}"
echo ""

# Main directories
create_directory "/usr/local/opt/octavia-ai"
create_directory "/usr/local/opt/octavia-ai/config"
create_directory "/usr/local/opt/octavia-ai/data"
create_directory "/usr/local/opt/octavia-ai/data/personas"
create_directory "/usr/local/opt/octavia-ai/data/audio"
create_directory "/usr/local/opt/octavia-ai/data/models"
create_directory "$HOME/Library/Logs/octavia-ai"

# Check for Homebrew
echo ""
echo -e "${BOLD}==== Checking for Homebrew ====${NC}"
echo ""

if command_exists brew; then
    echo -e "${GREEN}✓${NC} Homebrew is already installed"
    echo -e "ℹ Updating Homebrew..."
    brew update
    echo -e "${GREEN}✓${NC} Homebrew updated"
else
    echo -e "ℹ Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    if [ $? -ne 0 ]; then
        echo -e "${RED}✗${NC} Failed to install Homebrew. Please install manually."
        exit 1
    fi
    echo -e "${GREEN}✓${NC} Homebrew installed"
fi

# Install dependencies (skipping Python)
echo ""
echo -e "${BOLD}==== Installing System Dependencies ====${NC}"
echo ""

echo -e "${YELLOW}ℹ${NC} Skipping Python 3.11 installation as requested"
echo -e "${YELLOW}ℹ${NC} Please ensure Python 3.11 is installed manually"

# Check for Python 3.11
if command_exists python3.11; then
    echo -e "${GREEN}✓${NC} Python 3.11 is already installed"
else
    echo -e "${YELLOW}⚠${NC} Python 3.11 not found. Some features may not work correctly."
    echo -e "${YELLOW}⚠${NC} Please install Python 3.11 manually when possible."
fi

# Install Node.js
echo -e "ℹ Installing node@20..."
brew install node@20
if [ $? -ne 0 ]; then
    echo -e "${RED}✗${NC} Failed to install node@20. Please install manually."
    exit 1
fi
echo -e "${GREEN}✓${NC} node@20 installed"

# Install MongoDB
echo -e "ℹ Installing mongodb-community..."
brew tap mongodb/brew
brew install mongodb-community
if [ $? -ne 0 ]; then
    echo -e "${RED}✗${NC} Failed to install mongodb-community. Please install manually."
    exit 1
fi
echo -e "${GREEN}✓${NC} mongodb-community installed"

# Install Redis
echo -e "ℹ Installing redis..."
brew install redis
if [ $? -ne 0 ]; then
    echo -e "${RED}✗${NC} Failed to install redis. Please install manually."
    exit 1
fi
echo -e "${GREEN}✓${NC} redis installed"

# Install Nginx
echo -e "ℹ Installing nginx..."
brew install nginx
if [ $? -ne 0 ]; then
    echo -e "${RED}✗${NC} Failed to install nginx. Please install manually."
    exit 1
fi
echo -e "${GREEN}✓${NC} nginx installed"

# Install Python packages if Python is available
if command_exists python3.11; then
    echo ""
    echo -e "${BOLD}==== Installing Python Packages ====${NC}"
    echo ""
    
    echo -e "ℹ Installing required Python packages..."
    python3.11 -m pip install --upgrade pip
    python3.11 -m pip install flask requests openai python-dotenv twilio pydub numpy tensorflow transformers torch
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}⚠${NC} Some Python packages failed to install. You may need to install them manually."
    else
        echo -e "${GREEN}✓${NC} Python packages installed"
    fi
else
    echo ""
    echo -e "${BOLD}==== Skipping Python Packages Installation ====${NC}"
    echo ""
    echo -e "${YELLOW}ℹ${NC} Python 3.11 not found, skipping Python packages installation"
fi

# Install Node.js packages
echo ""
echo -e "${BOLD}==== Installing Node.js Packages ====${NC}"
echo ""

echo -e "ℹ Installing required Node.js packages..."
cd /usr/local/opt/octavia-ai
npm init -y
npm install express socket.io dotenv axios openai twilio @tryghost/content-api
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠${NC} Some Node.js packages failed to install. You may need to install them manually."
else
    echo -e "${GREEN}✓${NC} Node.js packages installed"
fi

# Configure AI Receptionist
echo ""
echo -e "${BOLD}==== Configuring AI Receptionist ====${NC}"
echo ""

# Create configuration file
echo -e "ℹ Creating configuration file..."
cat > /usr/local/opt/octavia-ai/config/.env << EOL
# Octavia AI Receptionist Configuration

# Ghost CMS Configuration
GHOST_API_URL=https://rainbow-millipede.pikapod.net
GHOST_CONTENT_API_KEY=your_content_api_key_here

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=(*************

# AI Configuration
AI_RECEPTIONIST_CONTENT_REFRESH_INTERVAL=3600
AI_RECEPTIONIST_MAX_CONTENT_AGE=86400
AI_RECEPTIONIST_CONTENT_LIMIT=100

# Server Configuration
PORT=3000
NODE_ENV=production
EOL
echo -e "${GREEN}✓${NC} Configuration file created"

# Create persona files
echo -e "ℹ Creating persona files..."

# The Grand Dame persona
cat > /usr/local/opt/octavia-ai/data/personas/grand_dame.json << EOL
{
  "name": "The Grand Dame",
  "description": "Elegant, sophisticated, and authoritative with a warm maternal energy",
  "voice_characteristics": {
    "tone": "warm, confident, elegant",
    "pace": "measured, deliberate",
    "pitch": "medium-low",
    "accent": "refined, slight British influence"
  },
  "linguistic_patterns": {
    "vocabulary": ["exquisite", "darling", "splendid", "refined", "distinguished"],
    "phrases": [
      "Welcome to Octavia Opulence, my dear.",
      "I'm delighted to assist you today.",
      "Allow me to guide you through our offerings."
    ],
    "sentence_structure": "formal, complete sentences with elegant phrasing"
  },
  "activation_triggers": [
    "general inquiries",
    "brand information",
    "luxury travel overview",
    "formal assistance"
  ]
}
EOL

# The Sage of Journeys persona
cat > /usr/local/opt/octavia-ai/data/personas/sage_of_journeys.json << EOL
{
  "name": "The Sage of Journeys",
  "description": "Worldly, knowledgeable, and contemplative with a calm, centered energy",
  "voice_characteristics": {
    "tone": "calm, thoughtful, measured",
    "pace": "unhurried, deliberate",
    "pitch": "medium",
    "accent": "neutral, with occasional global influences"
  },
  "linguistic_patterns": {
    "vocabulary": ["discover", "journey", "experience", "perspective", "authentic"],
    "phrases": [
      "As The Sage of Journeys, I'd recommend exploring...",
      "This destination offers a unique perspective...",
      "Consider the path less traveled..."
    ],
    "sentence_structure": "thoughtful, well-structured with descriptive elements"
  },
  "activation_triggers": [
    "destination inquiries",
    "travel planning",
    "cultural experiences",
    "safety information"
  ]
}
EOL

# The Spark of Connection persona
cat > /usr/local/opt/octavia-ai/data/personas/spark_of_connection.json << EOL
{
  "name": "The Spark of Connection",
  "description": "Energetic, enthusiastic, and socially vibrant with a youthful energy",
  "voice_characteristics": {
    "tone": "bright, enthusiastic, energetic",
    "pace": "quick, dynamic",
    "pitch": "medium-high",
    "accent": "contemporary, slight vocal fry"
  },
  "linguistic_patterns": {
    "vocabulary": ["amazing", "connect", "vibe", "community", "authentic"],
    "phrases": [
      "Hi there! The Spark of Connection here!",
      "I found an amazing experience you'll love!",
      "This is totally the perfect spot to connect!"
    ],
    "sentence_structure": "casual, sometimes fragmented, with contemporary expressions"
  },
  "activation_triggers": [
    "social experiences",
    "community events",
    "nightlife inquiries",
    "casual conversation"
  ]
}
EOL

# The Connoisseur of Joys persona
cat > /usr/local/opt/octavia-ai/data/personas/connoisseur_of_joys.json << EOL
{
  "name": "The Connoisseur of Joys",
  "description": "Sophisticated Gay Male archetype with discerning taste and effortless elegance",
  "voice_characteristics": {
    "tone": "smooth, cultured, slightly playful",
    "pace": "unhurried, deliberate",
    "pitch": "medium-low",
    "accent": "cosmopolitan, with subtle European influences"
  },
  "linguistic_patterns": {
    "vocabulary": ["divine", "exquisite", "indulge", "sublime", "curated"],
    "phrases": [
      "Ah, my dear, The Connoisseur of Joys at your service.",
      "This experience is simply divine, darling.",
      "One simply must indulge in the finer things."
    ],
    "sentence_structure": "refined, with occasional dramatic flourishes"
  },
  "activation_triggers": [
    "luxury experiences",
    "fine dining",
    "aesthetic inquiries",
    "shopping and fashion"
  ]
}
EOL
echo -e "${GREEN}✓${NC} Persona files created"

# Create web interface files
echo ""
echo -e "${BOLD}==== Creating Web Interface ====${NC}"
echo ""

# Create server.js
echo -e "ℹ Creating server files..."
cat > /usr/local/opt/octavia-ai/server.js << EOL
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const dotenv = require('dotenv');
const fs = require('fs');
const axios = require('axios');

// Load environment variables
dotenv.config({ path: path.join(__dirname, 'config', '.env') });

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Load personas
const personas = {
  grand_dame: JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'personas', 'grand_dame.json'), 'utf8')),
  sage_of_journeys: JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'personas', 'sage_of_journeys.json'), 'utf8')),
  spark_of_connection: JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'personas', 'spark_of_connection.json'), 'utf8')),
  connoisseur_of_joys: JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'personas', 'connoisseur_of_joys.json'), 'utf8'))
};

// Simple content cache
let contentCache = [];
let lastCacheUpdate = null;

// Function to determine which persona should respond
function determinePersona(query) {
  const lowerQuery = query.toLowerCase();
  
  // Travel and destination related
  if (lowerQuery.includes('destination') || 
      lowerQuery.includes('travel') || 
      lowerQuery.includes('location')) {
    return 'sage_of_journeys';
  }
  
  // Community and experience related
  if (lowerQuery.includes('experience') || 
      lowerQuery.includes('event') ||
      lowerQuery.includes('community')) {
    return 'spark_of_connection';
  }
  
  // Luxury and aesthetics related
  if (lowerQuery.includes('luxury') || 
      lowerQuery.includes('design') ||
      lowerQuery.includes('style')) {
    return 'connoisseur_of_joys';
  }
  
  // Default to Grand Dame
  return 'grand_dame';
}

// Function to generate response based on persona
function generateResponse(persona, query) {
  const personaData = personas[persona];
  
  // For demonstration, return a templated response
  const phrases = personaData.linguistic_patterns.phrases;
  const randomPhrase = phrases[Math.floor(Math.random() * phrases.length)];
  
  return {
    text: \`\${randomPhrase} I'd be happy to help you with "\${query}". This is a placeholder response until connected to the Ghost CMS content API.\`,
    persona: persona
  };
}

// Socket.io connection
io.on('connection', (socket) => {
  console.log('New client connected');
  
  // Send welcome message
  socket.emit('message', {
    text: personas.grand_dame.linguistic_patterns.phrases[0],
    persona: 'grand_dame'
  });
  
  // Handle incoming messages
  socket.on('message', (data) => {
    const persona = determinePersona(data.text);
    const response = generateResponse(persona, data.text);
    
    // Simulate typing delay
    setTimeout(() => {
      socket.emit('message', response);
    }, 1000);
  });
  
  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
  console.log(\`Server running on port \${PORT}\`);
});
EOL

# Create public directory and files
mkdir -p /usr/local/opt/octavia-ai/public
cat > /usr/local/opt/octavia-ai/public/index.html << EOL
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Octavia Opulence AI Receptionist</title>
  <link rel="stylesheet" href="styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Raleway:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
  <header>
    <div class="logo">
      <h1>Octavia Opulence</h1>
      <p>Luxury LGBTQ+ Travel</p>
    </div>
  </header>
  
  <main>
    <div class="receptionist-container">
      <div class="facet-selector">
        <button class="facet-button grand-dame active" data-facet="grand_dame">The Grand Dame</button>
        <button class="facet-button sage-journeys" data-facet="sage_of_journeys">The Sage of Journeys</button>
        <button class="facet-button spark-connection" data-facet="spark_of_connection">The Spark of Connection</button>
        <button class="facet-button connoisseur-joys" data-facet="connoisseur_of_joys">The Connoisseur of Joys</button>
      </div>
      
      <div class="chat-container">
        <div class="messages" id="messages"></div>
        
        <div class="input-area">
          <input type="text" id="message-input" placeholder="Ask me about luxury queer travel experiences...">
          <button id="send-button">Send</button>
        </div>
      </div>
    </div>
  </main>
  
  <footer>
    <p>&copy; 2025 Octavia Opulence. All rights reserved.</p>
  </footer>
  
  <script src="/socket.io/socket.io.js"></script>
  <script src="app.js"></script>
</body>
</html>
EOL

cat > /usr/local/opt/octavia-ai/public/styles.css << EOL
:root {
  --primary-color: #00585E;
  --secondary-color: #D4AF37;
  --background-color: #f5f5dc;
  --text-color: #333;
  --grand-dame-color: #4A2545;
  --sage-journeys-color: #336B87;
  --spark-connection-color: #FF6B6B;
  --connoisseur-joys-color: #800020;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Raleway', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

header {
  background-color: var(--primary-color);
  color: white;
  padding: 1rem 2rem;
  text-align: center;
}

.logo h1 {
  font-family: 'Playfair Display', serif;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.logo p {
  font-size: 1rem;
  font-weight: 300;
}

main {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.receptionist-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.facet-selector {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  background-color: #f9f9f9;
}

.facet-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.facet-button.active {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.facet-button.grand-dame {
  background-color: var(--grand-dame-color);
  color: white;
}

.facet-button.sage-journeys {
  background-color: var(--sage-journeys-color);
  color: white;
}

.facet-button.spark-connection {
  background-color: var(--spark-connection-color);
  color: white;
}

.facet-button.connoisseur-joys {
  background-color: var(--connoisseur-joys-color);
  color: white;
}

.chat-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.message {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 10px;
  max-width: 80%;
  line-height: 1.4;
}

.user-message {
  background-color: #E8E8E8;
  align-self: flex-end;
}

.ai-message {
  background-color: var(--primary-color);
  color: white;
  align-self: flex-start;
}

.ai-message.grand_dame {
  background-color: var(--grand-dame-color);
}

.ai-message.sage_of_journeys {
  background-color: var(--sage-journeys-color);
}

.ai-message.spark_of_connection {
  background-color: var(--spark-connection-color);
}

.ai-message.connoisseur_of_joys {
  background-color: var(--connoisseur-joys-color);
}

.input-area {
  display: flex;
  padding: 1rem;
  border-top: 1px solid #E8E8E8;
}

.input-area input {
  flex-grow: 1;
  padding: 0.75rem;
  border: 1px solid #E8E8E8;
  border-radius: 5px;
  margin-right: 0.5rem;
  font-size: 1rem;
}

.input-area button {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: none;
  border-radius: 5px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.input-area button:hover {
  background-color: #CFB53B;
}

footer {
  text-align: center;
  padding: 1rem;
  background-color: var(--primary-color);
  color: white;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .facet-selector {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .facet-button {
    flex: 1 1 40%;
    font-size: 0.9rem;
    padding: 0.4rem 0.6rem;
  }
}
EOL

cat > /usr/local/opt/octavia-ai/public/app.js << EOL
document.addEventListener('DOMContentLoaded', () => {
  const socket = io();
  const messagesContainer = document.getElementById('messages');
  const messageInput = document.getElementById('message-input');
  const sendButton = document.getElementById('send-button');
  const facetButtons = document.querySelectorAll('.facet-button');
  
  let activeFacet = 'grand_dame';
  
  // Handle incoming messages
  socket.on('message', (data) => {
    const messageElement = document.createElement('div');
    messageElement.classList.add('message', 'ai-message', data.persona);
    messageElement.textContent = data.text;
    messagesContainer.appendChild(messageElement);
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // Update active facet
    setActiveFacet(data.persona);
  });
  
  // Send message
  function sendMessage() {
    const message = messageInput.value.trim();
    if (message) {
      // Add user message to chat
      const messageElement = document.createElement('div');
      messageElement.classList.add('message', 'user-message');
      messageElement.textContent = message;
      messagesContainer.appendChild(messageElement);
      
      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
      
      // Send to server
      socket.emit('message', { text: message });
      
      // Clear input
      messageInput.value = '';
    }
  }
  
  // Set active facet
  function setActiveFacet(facet) {
    activeFacet = facet;
    
    // Update button styles
    facetButtons.forEach(button => {
      if (button.dataset.facet === facet) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }
  
  // Event listeners
  sendButton.addEventListener('click', sendMessage);
  
  messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  });
  
  facetButtons.forEach(button => {
    button.addEventListener('click', () => {
      setActiveFacet(button.dataset.facet);
    });
  });
});
EOL
echo -e "${GREEN}✓${NC} Web interface files created"

# Create startup script
echo -e "ℹ Creating startup script..."
cat > /usr/local/opt/octavia-ai/start.sh << EOL
#!/bin/bash

# Start the Octavia AI Receptionist
cd /usr/local/opt/octavia-ai
node server.js >> ~/Library/Logs/octavia-ai/server.log 2>&1 &
echo \$! > /usr/local/opt/octavia-ai/server.pid
echo "Octavia AI Receptionist started"
EOL
chmod +x /usr/local/opt/octavia-ai/start.sh
echo -e "${GREEN}✓${NC} Startup script created"

# Create launchd plist
echo -e "ℹ Creating launchd service..."
cat > ~/Library/LaunchAgents/com.octavia.ai.plist << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.octavia.ai</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/opt/octavia-ai/start.sh</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>~/Library/Logs/octavia-ai/launchd.log</string>
    <key>StandardErrorPath</key>
    <string>~/Library/Logs/octavia-ai/launchd_error.log</string>
</dict>
</plist>
EOL
echo -e "${GREEN}✓${NC} Launchd service created"

# Start services
echo ""
echo -e "${BOLD}==== Starting Services ====${NC}"
echo ""

echo -e "ℹ Starting MongoDB..."
brew services start mongodb-community
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠${NC} Failed to start MongoDB. You may need to start it manually."
else
    echo -e "${GREEN}✓${NC} MongoDB started"
fi

echo -e "ℹ Starting Redis..."
brew services start redis
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠${NC} Failed to start Redis. You may need to start it manually."
else
    echo -e "${GREEN}✓${NC} Redis started"
fi

echo -e "ℹ Starting Nginx..."
brew services start nginx
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠${NC} Failed to start Nginx. You may need to start it manually."
else
    echo -e "${GREEN}✓${NC} Nginx started"
fi

echo -e "ℹ Starting AI Receptionist..."
launchctl load ~/Library/LaunchAgents/com.octavia.ai.plist
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠${NC} Failed to start AI Receptionist via launchd. Starting manually..."
    /usr/local/opt/octavia-ai/start.sh
    if [ $? -ne 0 ]; then
        echo -e "${RED}✗${NC} Failed to start AI Receptionist. Please start it manually."
    else
        echo -e "${GREEN}✓${NC} AI Receptionist started manually"
    fi
else
    echo -e "${GREEN}✓${NC} AI Receptionist started via launchd"
fi

# Final instructions
echo ""
echo -e "${BOLD}==== Installation Complete ====${NC}"
echo ""
echo -e "The Octavia Opulence AI Receptionist has been installed and configured."
echo -e ""
echo -e "${BOLD}Next Steps:${NC}"
echo -e "1. Update the Ghost CMS API key in /usr/local/opt/octavia-ai/config/.env"
echo -e "2. Update the Twilio credentials in /usr/local/opt/octavia-ai/config/.env"
echo -e "3. Access the AI Receptionist at http://localhost:3000"
echo -e ""
echo -e "${BOLD}Useful Commands:${NC}"
echo -e "- Start services: brew services start mongodb-community redis nginx"
echo -e "- Stop services: brew services stop mongodb-community redis nginx"
echo -e "- Restart AI Receptionist: launchctl unload ~/Library/LaunchAgents/com.octavia.ai.plist && launchctl load ~/Library/LaunchAgents/com.octavia.ai.plist"
echo -e "- View logs: tail -f ~/Library/Logs/octavia-ai/server.log"
echo -e ""
echo -e "${GREEN}Thank you for installing the Octavia Opulence AI Receptionist!${NC}"
