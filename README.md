# QueerLuxe Travel Studio

A bespoke, luxurious travel platform by and for the LGBTQ+ community and allies.

## Project Overview

QueerLuxe Travel Studio crafts authentic, luxurious travel experiences for the LGBTQ+ community. This platform ensures confident travel to vetted, celebratory destinations, connects travelers with local queer culture, offers elevated experiences, and provides personalized service where clients are truly seen and understood.

## Core Values

- **Inclusivity**: Creating spaces where everyone belongs
- **Authenticity**: Celebrating genuine LGBTQ+ experiences
- **Luxury**: Providing exceptional quality and comfort
- **Empowerment**: Enabling confident, joyful travel
- **Community**: Fostering connections globally

## Repository Structure

```
queerluxe-travel-studio/
├── docs/                      # Documentation files
│   ├── requirements/          # Project requirements
│   ├── design/                # Design assets and guidelines
│   └── planning/              # Project planning documents
├── public/                    # Static assets
├── src/                       # Source code
│   ├── app/                   # Next.js app directory
│   ├── components/            # React components
│   ├── lib/                   # Utility functions and shared code
│   └── styles/                # Global styles
├── .env.example               # Example environment variables
├── .gitignore                 # Git ignore file
├── package.json               # Project dependencies
└── README.md                  # Project documentation
```

## Features

- **Core Website**: Homepage, blog, forum, pride events calendar, booking system
- **Luxury Travel Partners**: Fashion industry packages, premium transportation, Fora Perks
- **Specialized Travel Categories**: Ultra Glamping, Set Jetting, Honeymoons, Family-friendly trips
- **Admin Dashboard**: LLM agents for marketing, copywriting, chatbots, and voice
- **Client Portal**: User accounts, bookings, itineraries, communication tools

## Technology Stack

- **Frontend**: Next.js, React, Tailwind CSS
- **Backend**: Supabase (Authentication, Database, Storage)
- **Deployment**: Vercel/Netlify

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Git

### Installation

1. Clone the repository
   ```
   git clone https://github.com/yourusername/queerluxe-travel-studio.git
   cd queerluxe-travel-studio
   ```

2. Install dependencies
   ```
   npm install
   # or
   yarn install
   ```

3. Set up environment variables
   ```
   cp .env.example .env.local
   ```
   Edit `.env.local` to add your Supabase credentials and other environment variables.

4. Start the development server
   ```
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## GitHub Codespaces

This repository is configured for GitHub Codespaces, providing a complete development environment in the cloud.

To start a new Codespace:
1. Click the "Code" button on the repository page
2. Select the "Codespaces" tab
3. Click "Create codespace on main"

## Documentation

- [Requirements Analysis](docs/requirements/requirements_analysis.md)
- [Technical Analysis](docs/requirements/technical_analysis.md)
- [Brand Style Guide](docs/design/brand-style-guide.md)
- [User Flows](docs/planning/user-flows.md)
- [Implementation Plan](docs/planning/implementation-plan.md)
- [Luxury Partners Integration](docs/planning/luxury-partners-integration.md)
- [Fora Perks Integration](docs/planning/fora-perks-integration.md)
- [Specialized Travel Categories](docs/planning/specialized-travel-categories.md)

## License

This project is proprietary and confidential.

## Contact

For any inquiries, please contact [<EMAIL>](mailto:<EMAIL>).
