{"name": "queerluxe_travel", "version": "0.1.0", "private": true, "description": "A bespoke, luxurious travel platform by and for the LGBTQ+ community and allies.", "homepage": "https://github.com/QueerAgent1/theplatform#readme", "bugs": {"url": "https://github.com/QueerAgent1/theplatform/issues"}, "repository": {"type": "git", "url": "git+https://github.com/QueerAgent1/theplatform.git"}, "license": "ISC", "author": "", "type": "commonjs", "main": "next.config.js", "directories": {"doc": "docs"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@storybook/nextjs": "^9.0.4", "@supabase/supabase-js": "^2.39.3", "autoprefixer": "^10.4.17", "next": "^15.3.3", "postcss": "^8.4.35", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.1"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "18.3.23", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "typescript": "5.8.3"}}